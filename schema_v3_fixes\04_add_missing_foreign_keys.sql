-- ============================================================================
-- PHASE 4: ADD MISSING FOREIGN KEY CONSTRAINTS
-- ============================================================================
-- Add all the foreign key constraints that are missing from the current schema

-- Add missing icon foreign keys
ALTER TABLE transport_mode 
ADD CONSTRAINT fk_transport_mode_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

ALTER TABLE accommodation_type 
ADD CONSTRAINT fk_accommodation_type_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

ALTER TABLE activity_category 
ADD CONSTRAINT fk_activity_category_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

ALTER TABLE restaurant_type 
ADD CONSTRAINT fk_restaurant_type_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

ALTER TABLE cuisine_type 
ADD CONSTRAINT fk_cuisine_type_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

ALTER TABLE meal_type 
ADD CONSTRAINT fk_meal_type_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

ALTER TABLE tag 
ADD CONSTRAINT fk_tag_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

ALTER TABLE budget_category 
ADD CONSTRAINT fk_budget_category_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

ALTER TABLE amenity 
ADD CONSTRAINT fk_amenity_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

ALTER TABLE interest_category 
ADD CONSTRAINT fk_interest_category_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

ALTER TABLE guide_specialty 
ADD CONSTRAINT fk_guide_specialty_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

ALTER TABLE country_custom 
ADD CONSTRAINT fk_country_custom_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

-- Add missing location foreign keys
ALTER TABLE accommodation 
ADD CONSTRAINT fk_accommodation_location 
FOREIGN KEY (location_id) REFERENCES location(id);

ALTER TABLE activity 
ADD CONSTRAINT fk_activity_location 
FOREIGN KEY (location_id) REFERENCES location(id);

ALTER TABLE restaurant 
ADD CONSTRAINT fk_restaurant_location 
FOREIGN KEY (location_id) REFERENCES location(id);

ALTER TABLE station 
ADD CONSTRAINT fk_station_location 
FOREIGN KEY (location_id) REFERENCES location(id);

ALTER TABLE car_rental 
ADD CONSTRAINT fk_car_rental_location 
FOREIGN KEY (location_id) REFERENCES location(id);

-- Add missing city foreign keys
ALTER TABLE location 
ADD CONSTRAINT fk_location_city 
FOREIGN KEY (city_id) REFERENCES city(id);

ALTER TABLE guided_tour 
ADD CONSTRAINT fk_guided_tour_city 
FOREIGN KEY (city_id) REFERENCES city(id);

ALTER TABLE guide_location_junction 
ADD CONSTRAINT fk_guide_location_junction_city 
FOREIGN KEY (city_id) REFERENCES city(id);

ALTER TABLE trip_day 
ADD CONSTRAINT fk_trip_day_city 
FOREIGN KEY (city_id) REFERENCES city(id);

ALTER TABLE transport_line 
ADD CONSTRAINT fk_transport_line_city 
FOREIGN KEY (city_id) REFERENCES city(id);

ALTER TABLE transport_fare 
ADD CONSTRAINT fk_transport_fare_city 
FOREIGN KEY (city_id) REFERENCES city(id);

ALTER TABLE car_rental 
ADD CONSTRAINT fk_car_rental_city 
FOREIGN KEY (city_id) REFERENCES city(id);

ALTER TABLE taxi_fare 
ADD CONSTRAINT fk_taxi_fare_city 
FOREIGN KEY (city_id) REFERENCES city(id);
