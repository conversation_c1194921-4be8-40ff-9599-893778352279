-- ============================================================================
-- PHASE 5: MIGRATE TOUR IMAGES
-- ============================================================================
-- Migrate data from tour_image table to new system

-- Insert tour images into central image table
INSERT INTO image (url, alt_text, created_at, updated_at)
SELECT 
    image_url,
    caption,
    created_at,
    updated_at
FROM tour_image;

-- Create entity_image relationships for tours
INSERT INTO entity_image (image_id, entity_type, entity_id, image_type, sequence, caption, is_featured, created_at, updated_at)
SELECT 
    i.id,
    'tour',
    ti.tour_id,
    CASE WHEN ti.is_featured THEN 'featured' ELSE 'gallery' END,
    ti.sequence,
    ti.caption,
    ti.is_featured,
    ti.created_at,
    ti.updated_at
FROM tour_image ti
JOIN image i ON i.url = ti.image_url 
    AND i.created_at = ti.created_at;
