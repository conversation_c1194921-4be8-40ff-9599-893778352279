-- ============================================================================
-- PHASE 6: ADD REMAINING FOREIGN KEY CONSTRAINTS
-- ============================================================================
-- Add all other missing foreign key constraints

-- Country foreign keys
ALTER TABLE city 
ADD CONSTRAINT fk_city_country 
FOREIGN KEY (country_id) REFERENCES country(id);

ALTER TABLE country_emergency_contact 
ADD CONSTRAINT fk_country_emergency_contact_country 
FOREIGN KEY (country_id) REFERENCES country(id) ON DELETE CASCADE;

ALTER TABLE country_custom 
ADD CONSTRAINT fk_country_custom_country 
FOREIGN KEY (country_id) REFERENCES country(id) ON DELETE CASCADE;

-- User foreign keys
ALTER TABLE user_system_preference 
ADD CONSTRAINT fk_user_system_preference_user 
FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE;

ALTER TABLE user_interest_junction 
ADD CONSTRAINT fk_user_interest_junction_user 
FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE;

ALTER TABLE user_interest_junction 
ADD CONSTRAINT fk_user_interest_junction_interest 
FOREIGN KEY (interest_id) REFERENCES interest_category(id) ON DELETE CASCADE;

-- Guide foreign keys
ALTER TABLE guide_profile 
ADD CONSTRAINT fk_guide_profile_user 
FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE;

ALTER TABLE guide_language_junction 
ADD CONSTRAINT fk_guide_language_junction_guide 
FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE;

ALTER TABLE guide_language_junction 
ADD CONSTRAINT fk_guide_language_junction_language 
FOREIGN KEY (language_id) REFERENCES language(id);

ALTER TABLE guide_availability 
ADD CONSTRAINT fk_guide_availability_guide 
FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE;

ALTER TABLE guide_specialty 
ADD CONSTRAINT fk_guide_specialty_guide 
FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE;

ALTER TABLE guide_location_junction 
ADD CONSTRAINT fk_guide_location_junction_guide 
FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE;

ALTER TABLE guided_tour 
ADD CONSTRAINT fk_guided_tour_guide 
FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE;

ALTER TABLE guide_request 
ADD CONSTRAINT fk_guide_request_traveler 
FOREIGN KEY (traveler_id) REFERENCES "user"(id);

ALTER TABLE guide_request 
ADD CONSTRAINT fk_guide_request_guide 
FOREIGN KEY (guide_id) REFERENCES guide_profile(id);

ALTER TABLE guide_request 
ADD CONSTRAINT fk_guide_request_trip 
FOREIGN KEY (trip_id) REFERENCES trip(id);

ALTER TABLE guide_request 
ADD CONSTRAINT fk_guide_request_tour 
FOREIGN KEY (tour_id) REFERENCES guided_tour(id);

-- Accommodation foreign keys
ALTER TABLE accommodation 
ADD CONSTRAINT fk_accommodation_type 
FOREIGN KEY (type_id) REFERENCES accommodation_type(id);

ALTER TABLE accommodation_amenity_junction 
ADD CONSTRAINT fk_accommodation_amenity_junction_accommodation 
FOREIGN KEY (accommodation_id) REFERENCES accommodation(id) ON DELETE CASCADE;

ALTER TABLE accommodation_amenity_junction 
ADD CONSTRAINT fk_accommodation_amenity_junction_amenity 
FOREIGN KEY (amenity_id) REFERENCES amenity(id);

ALTER TABLE accommodation_room 
ADD CONSTRAINT fk_accommodation_room_accommodation 
FOREIGN KEY (accommodation_id) REFERENCES accommodation(id) ON DELETE CASCADE;

ALTER TABLE accommodation_rate 
ADD CONSTRAINT fk_accommodation_rate_accommodation 
FOREIGN KEY (accommodation_id) REFERENCES accommodation(id) ON DELETE CASCADE;

ALTER TABLE accommodation_rate 
ADD CONSTRAINT fk_accommodation_rate_room 
FOREIGN KEY (room_id) REFERENCES accommodation_room(id) ON DELETE CASCADE;
