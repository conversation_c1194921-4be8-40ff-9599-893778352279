-- ============================================================================
-- COMPREHENSIVE DATABASE SCHEMA V2 - UPDATED WITH ALL FIXES
-- ============================================================================
-- This file contains the complete, updated database schema incorporating:
-- 1. All transport integration fixes
-- 2. BIGINT standardization for all ID fields
-- 3. Proper foreign key relationships
-- 4. Image consolidation improvements
-- ============================================================================

-- ============================================================================
-- CORE ENTITIES
-- ============================================================================

-- Icon table (referenced by many entities)
CREATE TABLE icon (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Central Image Management System
CREATE TABLE image (
    id BIGSERIAL PRIMARY KEY,
    url TEXT NOT NULL,
    alt_text TEXT,
    file_size INTEGER,
    mime_type VARCHAR(50),
    width INTEGER,
    height INTEGER,
    storage_provider VARCHAR(50) DEFAULT 'local',
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Entity Image Junction (replaces multiple image tables)
CREATE TABLE entity_image (
    id BIGSERIAL PRIMARY KEY,
    image_id BIGINT NOT NULL,
    entity_type VARCHAR(50) NOT NULL CHECK (entity_type IN ('accommodation', 'activity', 'restaurant', 'tour', 'city', 'user', 'trip', 'room')),
    entity_id BIGINT NOT NULL,
    image_type VARCHAR(50) NOT NULL DEFAULT 'gallery' CHECK (image_type IN ('featured', 'gallery', 'thumbnail', 'hero', 'avatar', 'logo')),
    sequence INTEGER DEFAULT 0,
    caption TEXT,
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (image_id) REFERENCES image(id) ON DELETE CASCADE,
    UNIQUE (entity_type, entity_id, image_id),
    -- Ensure only one featured image per entity
    UNIQUE (entity_type, entity_id, is_featured) DEFERRABLE INITIALLY DEFERRED
);

-- Country
CREATE TABLE country (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    code CHAR(2) UNIQUE,
    currency CHAR(3) NOT NULL,
    language VARCHAR(50),
    visa_requirements TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Country Emergency Contacts
CREATE TABLE country_emergency_contact (
    id BIGSERIAL PRIMARY KEY,
    country_id BIGINT NOT NULL,
    service_type VARCHAR(50) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (country_id) REFERENCES country(id) ON DELETE CASCADE,
    UNIQUE (country_id, service_type)
);

-- Country Customs
CREATE TABLE country_custom (
    id BIGSERIAL PRIMARY KEY,
    country_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    icon_id BIGINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (country_id) REFERENCES country(id) ON DELETE CASCADE,
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- City
CREATE TABLE city (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    country_id BIGINT NOT NULL,
    timezone VARCHAR(50) NOT NULL,
    short_description TEXT,
    about TEXT,
    best_time_to_visit TEXT,
    average_budget_min NUMERIC(12,2),
    average_budget_max NUMERIC(12,2),
    recommended_stay_min INTEGER,
    recommended_stay_max INTEGER,
    video_url TEXT,
    map_embed_url TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (country_id) REFERENCES country(id),
    UNIQUE (name, country_id)
);

-- Location
CREATE TABLE location (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    address TEXT,
    city_id BIGINT,
    lat DOUBLE PRECISION NOT NULL,
    lng DOUBLE PRECISION NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('hotel', 'restaurant', 'activity', 'airport', 'station', 'point_of_interest')),
    place_id VARCHAR(100) UNIQUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    UNIQUE(lat, lng),
    FOREIGN KEY (city_id) REFERENCES city(id)
);

-- Language
CREATE TABLE language (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- ============================================================================
-- USER MANAGEMENT
-- ============================================================================

-- User
CREATE TABLE "user" (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20) UNIQUE,
    role VARCHAR(20) NOT NULL DEFAULT 'traveler' CHECK (role IN ('traveler', 'guide', 'admin', 'both')),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- User System Preferences
CREATE TABLE user_system_preference (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    default_currency CHAR(3) NOT NULL DEFAULT 'USD',
    default_language VARCHAR(50) NOT NULL DEFAULT 'English',
    timezone VARCHAR(50) NOT NULL DEFAULT 'UTC',
    date_format VARCHAR(20) NOT NULL DEFAULT 'YYYY-MM-DD',
    email_notifications BOOLEAN NOT NULL DEFAULT true,
    sms_notifications BOOLEAN NOT NULL DEFAULT false,
    push_notifications BOOLEAN NOT NULL DEFAULT true,
    trip_updates BOOLEAN NOT NULL DEFAULT true,
    marketing BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE
);

-- Interest Categories
CREATE TABLE interest_category (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    icon_id BIGINT,
    description TEXT,
    type VARCHAR(50) NOT NULL CHECK (type IN ('travel_style', 'accommodation', 'activity', 'cuisine', 'general')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- User Interest Junction
CREATE TABLE user_interest_junction (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    interest_id BIGINT NOT NULL,
    weight INTEGER NOT NULL DEFAULT 5 CHECK (weight >= 1 AND weight <= 10),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE,
    FOREIGN KEY (interest_id) REFERENCES interest_category(id) ON DELETE CASCADE,
    UNIQUE (user_id, interest_id)
);

-- ============================================================================
-- GUIDE PROFILES
-- ============================================================================

-- Guide Profile
CREATE TABLE guide_profile (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    bio TEXT NOT NULL,
    specialization VARCHAR(200) NOT NULL,
    experience_years INTEGER NOT NULL,
    hourly_rate NUMERIC(10,2) NOT NULL,
    full_day_rate NUMERIC(10,2) NOT NULL,
    category VARCHAR(100),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE
);

-- Guide Language Junction
CREATE TABLE guide_language_junction (
    id BIGSERIAL PRIMARY KEY,
    guide_id BIGINT NOT NULL,
    language_id BIGINT NOT NULL,
    proficiency VARCHAR(20) NOT NULL CHECK (proficiency IN ('basic', 'conversational', 'fluent', 'native')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE,
    FOREIGN KEY (language_id) REFERENCES language(id),
    UNIQUE (guide_id, language_id)
);

-- Guide Availability
CREATE TABLE guide_availability (
    id BIGSERIAL PRIMARY KEY,
    guide_id BIGINT NOT NULL,
    date DATE NOT NULL,
    available_from TIME,
    available_to TIME,
    is_full_day BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE,
    UNIQUE (guide_id, date)
);

-- Guide Specialty
CREATE TABLE guide_specialty (
    id BIGSERIAL PRIMARY KEY,
    guide_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    icon_id BIGINT,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE,
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Guide Location Junction
CREATE TABLE guide_location_junction (
    id BIGSERIAL PRIMARY KEY,
    guide_id BIGINT NOT NULL,
    city_id BIGINT NOT NULL,
    is_primary BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE,
    FOREIGN KEY (city_id) REFERENCES city(id),
    UNIQUE (guide_id, city_id)
);

-- ============================================================================
-- TRANSPORTATION ENTITIES
-- ============================================================================

-- Transport Mode
CREATE TABLE transport_mode (
    id BIGSERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    icon_id BIGINT,
    description TEXT,
    is_public BOOLEAN NOT NULL DEFAULT TRUE,
    is_local BOOLEAN NOT NULL DEFAULT TRUE,
    allows_free_transfers BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Transport Provider
CREATE TABLE transport_provider (
    id BIGSERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    website TEXT,
    contact_phone TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Station
CREATE TABLE station (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    location_id BIGINT NOT NULL,
    station_type TEXT NOT NULL,
    is_transfer_point BOOLEAN NOT NULL DEFAULT FALSE,
    station_code CHAR(3) UNIQUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (location_id) REFERENCES location(id)
);

-- Transport Line
CREATE TABLE transport_line (
    id BIGSERIAL PRIMARY KEY,
    provider_id BIGINT NOT NULL,
    mode_id BIGINT NOT NULL,
    line_number TEXT NOT NULL,
    name TEXT,
    city_id BIGINT,
    is_intercity BOOLEAN NOT NULL DEFAULT FALSE,
    train_type TEXT,
    color TEXT,
    fare_type TEXT NOT NULL DEFAULT 'fixed',
    fare_amount NUMERIC(12,2),
    currency CHAR(3),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (provider_id) REFERENCES transport_provider(id),
    FOREIGN KEY (mode_id) REFERENCES transport_mode(id),
    FOREIGN KEY (city_id) REFERENCES city(id),
    UNIQUE (provider_id, line_number, city_id)
);

-- Line Station Junction
CREATE TABLE line_station_junction (
    id BIGSERIAL PRIMARY KEY,
    line_id BIGINT NOT NULL,
    station_id BIGINT NOT NULL,
    sequence INT NOT NULL,
    is_terminus BOOLEAN NOT NULL DEFAULT FALSE,
    platform TEXT,
    travel_time_to_next INT,
    distance_to_next NUMERIC(8,2),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (line_id) REFERENCES transport_line(id),
    FOREIGN KEY (station_id) REFERENCES station(id),
    UNIQUE (line_id, station_id, sequence)
);

-- Station Transfer
CREATE TABLE station_transfer (
    id BIGSERIAL PRIMARY KEY,
    from_station_id BIGINT NOT NULL,
    to_station_id BIGINT NOT NULL,
    transfer_time_minutes INT NOT NULL,
    transfer_type TEXT NOT NULL DEFAULT 'walking',
    is_free BOOLEAN NOT NULL DEFAULT TRUE,
    transfer_instructions TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (from_station_id) REFERENCES station(id),
    FOREIGN KEY (to_station_id) REFERENCES station(id),
    UNIQUE (from_station_id, to_station_id)
);

-- Transport Fare
CREATE TABLE transport_fare (
    id BIGSERIAL PRIMARY KEY,
    mode_id BIGINT NOT NULL,
    provider_id BIGINT,
    from_station_id BIGINT,
    to_station_id BIGINT,
    city_id BIGINT,
    line_id BIGINT,
    service_class TEXT,
    price NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL,
    valid_minutes INT,
    duration_minutes INT,
    distance_km NUMERIC(8,2),
    departure_time TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (mode_id) REFERENCES transport_mode(id),
    FOREIGN KEY (provider_id) REFERENCES transport_provider(id),
    FOREIGN KEY (from_station_id) REFERENCES station(id),
    FOREIGN KEY (to_station_id) REFERENCES station(id),
    FOREIGN KEY (city_id) REFERENCES city(id),
    FOREIGN KEY (line_id) REFERENCES transport_line(id)
);

-- Car Rental
CREATE TABLE car_rental (
    id BIGSERIAL PRIMARY KEY,
    provider_id BIGINT NOT NULL,
    city_id BIGINT NOT NULL,
    location_id BIGINT NOT NULL,
    car_type TEXT NOT NULL,
    daily_rate NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL,
    insurance_cost NUMERIC(12,2),
    is_airport_pickup BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (provider_id) REFERENCES transport_provider(id),
    FOREIGN KEY (city_id) REFERENCES city(id),
    FOREIGN KEY (location_id) REFERENCES location(id)
);

-- Taxi Fare
CREATE TABLE taxi_fare (
    id BIGSERIAL PRIMARY KEY,
    city_id BIGINT NOT NULL,
    base_fare NUMERIC(10,2) NOT NULL,
    per_km_rate NUMERIC(10,2) NOT NULL,
    per_minute_rate NUMERIC(10,2),
    currency CHAR(3) NOT NULL,
    provider_id BIGINT,
    taxi_type TEXT DEFAULT 'petit_taxi',
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (city_id) REFERENCES city(id),
    FOREIGN KEY (provider_id) REFERENCES transport_provider(id)
);

-- ============================================================================
-- ACCOMMODATION ENTITIES
-- ============================================================================

-- Accommodation Type
CREATE TABLE accommodation_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    icon_id BIGINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Accommodation
CREATE TABLE accommodation (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    location_id BIGINT NOT NULL,
    type_id BIGINT NOT NULL,
    stars INTEGER CHECK (stars >= 1 AND stars <= 5),
    average_score NUMERIC(3,1) CHECK (average_score >= 0 AND average_score <= 10),
    description TEXT,
    check_in_time TIME,
    check_out_time TIME,
    website TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (location_id) REFERENCES location(id),
    FOREIGN KEY (type_id) REFERENCES accommodation_type(id)
);

-- Amenity
CREATE TABLE amenity (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    category VARCHAR(50) NOT NULL,
    icon_id BIGINT,
    description TEXT,
    amenity_type VARCHAR(20) NOT NULL DEFAULT 'both' CHECK (amenity_type IN ('accommodation', 'restaurant', 'both')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Accommodation Amenity Junction
CREATE TABLE accommodation_amenity_junction (
    id BIGSERIAL PRIMARY KEY,
    accommodation_id BIGINT NOT NULL,
    amenity_id BIGINT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (accommodation_id) REFERENCES accommodation(id) ON DELETE CASCADE,
    FOREIGN KEY (amenity_id) REFERENCES amenity(id),
    UNIQUE (accommodation_id, amenity_id)
);

-- Accommodation Room
CREATE TABLE accommodation_room (
    id BIGSERIAL PRIMARY KEY,
    accommodation_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    max_guests INTEGER NOT NULL,
    beds_description TEXT,
    size_sqm INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (accommodation_id) REFERENCES accommodation(id) ON DELETE CASCADE
);

-- Accommodation Rate
CREATE TABLE accommodation_rate (
    id BIGSERIAL PRIMARY KEY,
    accommodation_id BIGINT NOT NULL,
    room_id BIGINT NOT NULL,
    date_from DATE NOT NULL,
    date_to DATE NOT NULL,
    base_price NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL,
    breakfast_included BOOLEAN NOT NULL DEFAULT false,
    refundable BOOLEAN NOT NULL DEFAULT true,
    cancellation_policy TEXT,
    availability INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (accommodation_id) REFERENCES accommodation(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES accommodation_room(id) ON DELETE CASCADE
);

-- Activity Category
CREATE TABLE activity_category (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon_id BIGINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Activity
CREATE TABLE activity (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    location_id BIGINT NOT NULL,
    category_id BIGINT NOT NULL,
    description TEXT NOT NULL,
    duration_minutes INTEGER NOT NULL,
    price_adult NUMERIC(10,2),
    price_child NUMERIC(10,2),
    currency CHAR(3),
    website TEXT,
    phone VARCHAR(20),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (location_id) REFERENCES location(id),
    FOREIGN KEY (category_id) REFERENCES activity_category(id)
);

-- Activity Opening Hours
CREATE TABLE activity_opening_hours (
    id BIGSERIAL PRIMARY KEY,
    activity_id BIGINT NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    opening_time TIME,
    closing_time TIME,
    is_closed BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (activity_id) REFERENCES activity(id) ON DELETE CASCADE,
    UNIQUE (activity_id, day_of_week)
);

-- Tag
CREATE TABLE tag (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    category VARCHAR(50),
    icon_id BIGINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Activity Tag Junction
CREATE TABLE activity_tag_junction (
    id BIGSERIAL PRIMARY KEY,
    activity_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (activity_id) REFERENCES activity(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tag(id),
    UNIQUE (activity_id, tag_id)
);

-- ============================================================================
-- FOOD & DINING ENTITIES
-- ============================================================================

-- Cuisine Type
CREATE TABLE cuisine_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    icon_id BIGINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Restaurant Type
CREATE TABLE restaurant_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    icon_id BIGINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Restaurant
CREATE TABLE restaurant (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    location_id BIGINT NOT NULL,
    type_id BIGINT NOT NULL,
    price_range INTEGER NOT NULL CHECK (price_range >= 1 AND price_range <= 4),
    rating NUMERIC(2,1) CHECK (rating >= 0 AND rating <= 5),
    description TEXT,
    website TEXT,
    phone VARCHAR(20),
    menu_url TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (location_id) REFERENCES location(id),
    FOREIGN KEY (type_id) REFERENCES restaurant_type(id)
);

-- Restaurant Cuisine Junction
CREATE TABLE restaurant_cuisine_junction (
    id BIGSERIAL PRIMARY KEY,
    restaurant_id BIGINT NOT NULL,
    cuisine_id BIGINT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE,
    FOREIGN KEY (cuisine_id) REFERENCES cuisine_type(id),
    UNIQUE (restaurant_id, cuisine_id)
);

-- Restaurant Amenity Junction
CREATE TABLE restaurant_amenity_junction (
    id BIGSERIAL PRIMARY KEY,
    restaurant_id BIGINT NOT NULL,
    amenity_id BIGINT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE,
    FOREIGN KEY (amenity_id) REFERENCES amenity(id),
    UNIQUE (restaurant_id, amenity_id)
);

-- Restaurant Opening Hours
CREATE TABLE restaurant_opening_hours (
    id BIGSERIAL PRIMARY KEY,
    restaurant_id BIGINT NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    opening_time TIME,
    closing_time TIME,
    is_closed BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE,
    UNIQUE (restaurant_id, day_of_week)
);

-- Meal Type
CREATE TABLE meal_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    icon_id BIGINT,
    typical_time_start TIME,
    typical_time_end TIME,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);
