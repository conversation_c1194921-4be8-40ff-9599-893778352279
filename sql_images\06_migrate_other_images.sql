-- ============================================================================
-- PHASE 6: MIGRATE OTHER IMAGE FIELDS
-- ============================================================================
-- Migrate hero images, avatars, and other single image fields

-- Migrate city hero images
INSERT INTO image (url, alt_text, created_at, updated_at)
SELECT 
    hero_image,
    CONCAT('Hero image for ', name),
    created_at,
    updated_at
FROM city 
WHERE hero_image IS NOT NULL AND hero_image != '';

INSERT INTO entity_image (image_id, entity_type, entity_id, image_type, sequence, caption, is_featured, created_at, updated_at)
SELECT 
    i.id,
    'city',
    c.id,
    'hero',
    0,
    CONCAT('Hero image for ', c.name),
    true,
    c.created_at,
    c.updated_at
FROM city c
JOIN image i ON i.url = c.hero_image 
    AND i.created_at = c.created_at
WHERE c.hero_image IS NOT NULL AND c.hero_image != '';

-- Migrate user avatar images
INSERT INTO image (url, alt_text, created_at, updated_at)
SELECT 
    avatar_url,
    CONCAT('Avatar for ', name),
    created_at,
    updated_at
FROM "user" 
WHERE avatar_url IS NOT NULL AND avatar_url != '';

INSERT INTO entity_image (image_id, entity_type, entity_id, image_type, sequence, caption, is_featured, created_at, updated_at)
SELECT 
    i.id,
    'user',
    u.id,
    'avatar',
    0,
    CONCAT('Avatar for ', u.name),
    true,
    u.created_at,
    u.updated_at
FROM "user" u
JOIN image i ON i.url = u.avatar_url 
    AND i.created_at = u.created_at
WHERE u.avatar_url IS NOT NULL AND u.avatar_url != '';

-- Migrate trip images
INSERT INTO image (url, alt_text, created_at, updated_at)
SELECT 
    image_url,
    CONCAT('Trip image for ', title),
    created_at,
    updated_at
FROM trip 
WHERE image_url IS NOT NULL AND image_url != '';

INSERT INTO entity_image (image_id, entity_type, entity_id, image_type, sequence, caption, is_featured, created_at, updated_at)
SELECT 
    i.id,
    'trip',
    t.id,
    'featured',
    0,
    CONCAT('Trip image for ', t.title),
    true,
    t.created_at,
    t.updated_at
FROM trip t
JOIN image i ON i.url = t.image_url 
    AND i.created_at = t.created_at
WHERE t.image_url IS NOT NULL AND t.image_url != '';

-- Migrate transport provider logos
INSERT INTO image (url, alt_text, created_at, updated_at)
SELECT 
    logo_url,
    CONCAT('Logo for ', name),
    created_at,
    updated_at
FROM transport_provider 
WHERE logo_url IS NOT NULL AND logo_url != '';

INSERT INTO entity_image (image_id, entity_type, entity_id, image_type, sequence, caption, is_featured, created_at, updated_at)
SELECT 
    i.id,
    'provider',
    tp.id,
    'logo',
    0,
    CONCAT('Logo for ', tp.name),
    true,
    tp.created_at,
    tp.updated_at
FROM transport_provider tp
JOIN image i ON i.url = tp.logo_url 
    AND i.created_at = tp.created_at
WHERE tp.logo_url IS NOT NULL AND tp.logo_url != '';
