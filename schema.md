### User
* id: Unique identifier (PK)
* email: User email (UNIQUE, NOT NULL)
* name: Full name (NOT NULL)
* password_hash: Encrypted password (NOT NULL)
* phone: Phone number (UNIQUE)
* avatar_url: Profile image URL
* role: "traveler", "guide", "admin", "both" (NOT NULL, DEFAULT "traveler")
* status: "active", "inactive", "suspended" (NOT NULL, DEFAULT "active")
* created_at, updated_at: Timestamps

### UserSystemPreference
* id: Unique identifier (PK)
* user_id: Reference to User (FK, NOT NULL)
* default_currency: Preferred currency (NOT NULL, DEFAULT "USD")
* default_language: Preferred language (NOT NULL, DEFAULT "English")
* timezone: User timezone (NOT NULL, DEFAULT "UTC")
* date_format: Preferred date format (NOT NULL, DEFAULT "YYYY-MM-DD")
* email_notifications: Boolean (NOT NULL, DEFAULT true)
* sms_notifications: Boolean (NOT NULL, DEFAULT false)
* push_notifications: Boolean (NOT NULL, DEFAULT true)
* trip_updates: Boolean (NOT NULL, DEFAULT true)
* marketing: Boolean (NOT NULL, DEFAULT false)
* created_at, updated_at: Timestamps

## User Preferences

### InterestCategory
* id: Unique identifier (PK)
* name: Category name (UNIQUE, NOT NULL)
* icon_id: Reference to Icon (FK)
* description: Brief description
* type: "travel_style", "accommodation", "activity", "cuisine", "general" (NOT NULL)
* created_at, updated_at: Timestamps

### Icon
* id: Unique identifier (PK)
* name: Icon identifier (UNIQUE, NOT NULL)
* created_at, updated_at: Timestamps

### UserInterestJunction
* id: Unique identifier (PK)
* user_id: Reference to User (FK, NOT NULL)
* interest_id: Reference to InterestCategory (FK, NOT NULL)
* weight: Preference strength (1-10) (NOT NULL, DEFAULT 5)
* created_at, updated_at: Timestamps
* UNIQUE (user_id, interest_id)

## Guide Profile

### GuideProfile
* id: Unique identifier (PK)
* user_id: Reference to User (FK, NOT NULL, UNIQUE)
* bio: Detailed biography (NOT NULL)
* specialization: Primary expertise (NOT NULL)
* experience_years: Years of experience (NOT NULL)
* hourly_rate: Hourly charge (NOT NULL)
* full_day_rate: Full day charge (NOT NULL)
* category: Primary category (culture, nature, adventure, etc.)
* created_at, updated_at: Timestamps

### GuideLanguageJunction
* id: Unique identifier (PK)
* guide_id: Reference to GuideProfile (FK, NOT NULL)
* language_id: Reference to Language (FK, NOT NULL)
* proficiency: "basic", "conversational", "fluent", "native" (NOT NULL)
* created_at, updated_at: Timestamps
* UNIQUE (guide_id, language_id)

### Language
* id: Unique identifier (PK)
* name: Language name (UNIQUE, NOT NULL)
* created_at, updated_at: Timestamps

### GuideAvailability
* id: Unique identifier (PK)
* guide_id: Reference to GuideProfile (FK, NOT NULL)
* date: Available date (NOT NULL)
* available_from: Start time
* available_to: End time
* is_full_day: Boolean (NOT NULL, DEFAULT true)
* created_at, updated_at: Timestamps
* UNIQUE (guide_id, date)

### GuideSpecialty
* id: Unique identifier (PK)
* guide_id: Reference to GuideProfile (FK, NOT NULL)
* name: Specialty name (NOT NULL)
* icon_id: Reference to Icon (FK)
* description: Brief description
* created_at, updated_at: Timestamps

### GuideLocationJunction
* id: Unique identifier (PK)
* guide_id: Reference to GuideProfile (FK, NOT NULL)
* city_id: Reference to City (FK, NOT NULL)
* is_primary: Whether this is guide's primary location (NOT NULL, DEFAULT false)
* created_at, updated_at: Timestamps
* UNIQUE (guide_id, city_id)

## Trip Management

### Trip
* id: Unique identifier (PK)
* user_id: Reference to User (FK, NOT NULL)
* title: Trip name (NOT NULL)
* description: Trip description
* image_url: Featured image URL
* start_date: Check-in date (NOT NULL)
* end_date: Check-out date (NOT NULL)
* budget_total: Total budget amount (NOT NULL)
* currency: Currency code (NOT NULL)
* origin_airport_id: Reference to Station (FK)
* status: "draft", "confirmed", "in_progress", "completed", "cancelled" (NOT NULL, DEFAULT "draft")
* created_at, updated_at: Timestamps

### TripChangeLog
* id: Unique identifier (PK)
* trip_id: Reference to Trip (FK, NOT NULL)
* user_id: Reference to User who made the change (FK, NOT NULL)
* timestamp: When change occurred (NOT NULL, DEFAULT CURRENT_TIMESTAMP)
* change_type: "created", "updated", "cancelled", "rescheduled", "budget_changed", "item_added", "item_removed", "item_modified" (NOT NULL)
* entity_type: "trip", "accommodation", "activity", "meal", "transport" (NOT NULL)
* entity_id: ID of the changed entity (NOT NULL)
* field_name: Name of field that changed (NOT NULL)
* old_value: Previous value (can be NULL for new items)
* new_value: New value
* change_reason: Reason for change
* system_note: Internal note for system/admin use
* user_note: Note visible to user
* created_at, updated_at: Timestamps

### TripInterestJunction
* id: Unique identifier (PK)
* trip_id: Reference to Trip (FK, NOT NULL)
* interest_id: Reference to InterestCategory (FK, NOT NULL)
* weight: Preference strength value (1-10) (NOT NULL, DEFAULT 5)
* created_at, updated_at: Timestamps
* UNIQUE (trip_id, interest_id)

### TripDay
* id: Unique identifier (PK)
* trip_id: Reference to Trip (FK, NOT NULL)
* day_number: Sequence in trip (1, 2, 3...) (NOT NULL)
* date: Actual calendar date (NOT NULL)
* city_id: Reference to City (location for this day) (FK, NOT NULL)
* daily_budget: Allocated budget for this day
* created_at, updated_at: Timestamps
* UNIQUE (trip_id, day_number)
* UNIQUE (trip_id, date)

### DailyItinerary
* id: Unique identifier (PK)
* trip_day_id: Reference to TripDay (FK, NOT NULL, UNIQUE)
* created_at, updated_at: Timestamps

### DailyItineraryItem
* id: Unique identifier (PK)
* daily_itinerary_id: Reference to DailyItinerary (FK, NOT NULL)
* sequence: Order in itinerary (NOT NULL)
* item_type: "activity", "meal", "transport", "free_time", "guided_tour" (NOT NULL)
* item_id: ID of referenced item
* start_time: Start time for this item
* end_time: End time for this item
* notes: Special notes
* created_at, updated_at: Timestamps

## Location Entities

### Country
* id: Unique identifier (PK)
* name: Country name (UNIQUE, NOT NULL)
* code: Country code (ISO) (UNIQUE, NOT NULL)
* currency: Default currency (NOT NULL)
* language: Primary language
* visa_requirements: Text description
* created_at, updated_at: Timestamps

### CountryEmergencyContact
* id: Unique identifier (PK)
* country_id: Reference to Country (FK, NOT NULL)
* service_type: "police", "ambulance", "fire", "tourist_police", etc. (NOT NULL)
* phone_number: Emergency contact number (NOT NULL)
* notes: Additional information
* created_at, updated_at: Timestamps
* UNIQUE (country_id, service_type)

### CountryCustom
* id: Unique identifier (PK)
* country_id: Reference to Country (FK, NOT NULL)
* title: Custom title (NOT NULL)
* description: Custom description (NOT NULL)
* icon_id: Reference to Icon (FK)
* created_at, updated_at: Timestamps

### City
* id: Unique identifier (PK)
* name: City name (NOT NULL)
* country_id: Reference to Country (FK, NOT NULL)
* timezone: City timezone (NOT NULL)
* hero_image: URL to featured image
* short_description: Brief overview
* about: Detailed description
* best_time_to_visit: Recommended seasons
* average_budget_min: Typical daily budget minimum
* average_budget_max: Typical daily budget maximum
* recommended_stay_min: Suggested minimum days
* recommended_stay_max: Suggested maximum days
* video_url: Embedded video URL
* map_embed_url: Google Maps embed URL
* created_at, updated_at: Timestamps
* UNIQUE (name, country_id)

### Location
* id: Unique identifier (PK)
* name: Location name (NOT NULL)
* address: Street address
* city_id: Reference to City (FK, NOT NULL)
* lat: Latitude coordinate (NOT NULL)
* lng: Longitude coordinate (NOT NULL)
* type: "hotel", "restaurant", "activity", "airport", "station", "point_of_interest" (NOT NULL)
* place_id: Reference to external mapping service ID (UNIQUE)
* created_at, updated_at: Timestamps

## Guide Tour Entities

### GuidedTour
* id: Unique identifier (PK)
* guide_id: Reference to GuideProfile (FK, NOT NULL)
* city_id: Reference to City (FK, NOT NULL)
* title: Tour title (NOT NULL)
* description: Tour description (NOT NULL)
* duration_hours: Tour length (NOT NULL)
* max_travelers: Maximum capacity (NOT NULL, DEFAULT 10)
* base_price: Starting price (NOT NULL)
* currency: Currency code (NOT NULL, DEFAULT "USD")
* meeting_point: Meeting location description
* includes: What's included
* excludes: What's excluded
* is_private: Whether tour is private (NOT NULL, DEFAULT false)
* status: "active", "inactive" (NOT NULL, DEFAULT "active")
* created_at, updated_at: Timestamps

### TourHighlight
* id: Unique identifier (PK)
* tour_id: Reference to GuidedTour (FK, NOT NULL)
* title: Highlight title (NOT NULL)
* description: Highlight description (NOT NULL)
* sequence: Display order (NOT NULL, DEFAULT 0)
* created_at, updated_at: Timestamps

### TourImage
* id: Unique identifier (PK)
* tour_id: Reference to GuidedTour (FK, NOT NULL)
* image_url: Image URL (NOT NULL)
* caption: Image caption
* is_featured: Whether image should be featured (DEFAULT false)
* sequence: Display order (DEFAULT 0)
* created_at, updated_at: Timestamps

### TourItineraryPoint
* id: Unique identifier (PK)
* tour_id: Reference to GuidedTour (FK, NOT NULL)
* title: Point title (NOT NULL)
* description: Point description (NOT NULL)
* duration_minutes: Time spent (NOT NULL)
* sequence: Order in itinerary (NOT NULL)
* created_at, updated_at: Timestamps

### GuideRequest
* id: Unique identifier (PK)
* traveler_id: Reference to User (traveler) (FK, NOT NULL)
* guide_id: Reference to GuideProfile (FK, NOT NULL)
* trip_id: Reference to Trip (FK)
* tour_id: Reference to GuidedTour (FK)
* title: Tour title (NOT NULL)
* description: Tour description
* date: Requested date (NOT NULL)
* start_time: Start time (NOT NULL)
* end_time: End time (NOT NULL)
* travelers_count: Number of travelers (NOT NULL, DEFAULT 1)
* amount: Total cost (NOT NULL)
* currency: Currency code (NOT NULL, DEFAULT "USD")
* status: "pending", "accepted", "rejected", "completed", "cancelled" (NOT NULL, DEFAULT "pending")
* created_at, updated_at: Timestamps

## Accommodation Entities

### AccommodationType
* id: Unique identifier (PK)
* name: Type name (Hotel, Hostel, Apartment, etc.) (UNIQUE, NOT NULL)
* description: Brief description
* icon_id: Reference to Icon (FK)
* created_at, updated_at: Timestamps

### Accommodation
* id: Unique identifier (PK)
* name: Property name (NOT NULL)
* location_id: Reference to Location (FK, NOT NULL)
* type_id: Reference to AccommodationType (FK, NOT NULL)
* stars: Rating (1-5)
* average_score: Review score (0-10)
* description: Property description
* check_in_time: Standard check-in time
* check_out_time: Standard check-out time
* website: Official website
* phone: Contact number
* email: Contact email
* created_at, updated_at: Timestamps

### AccommodationImage
* id: Unique identifier (PK)
* accommodation_id: Reference to Accommodation (FK, NOT NULL)
* image_url: Image URL (NOT NULL)
* caption: Image caption
* is_featured: Whether image should be featured (DEFAULT false)
* sequence: Display order (DEFAULT 0)
* created_at, updated_at: Timestamps

### Amenity
* id: Unique identifier (PK)
* name: Amenity name (UNIQUE, NOT NULL)
* category: Category of amenity (NOT NULL)
* icon_id: Reference to Icon (FK)
* description: Brief description
* amenity_type: "accommodation", "restaurant", "both" (NOT NULL, DEFAULT "both")
* created_at, updated_at: Timestamps

### AccommodationAmenityJunction
* id: Unique identifier (PK)
* accommodation_id: Reference to Accommodation (FK, NOT NULL)
* amenity_id: Reference to Amenity (FK, NOT NULL)
* created_at, updated_at: Timestamps
* UNIQUE (accommodation_id, amenity_id)

### RestaurantAmenityJunction
* id: Unique identifier (PK)
* restaurant_id: Reference to Restaurant (FK, NOT NULL)
* amenity_id: Reference to Amenity (FK, NOT NULL)
* created_at, updated_at: Timestamps
* UNIQUE (restaurant_id, amenity_id)

### AccommodationRoom
* id: Unique identifier (PK)
* accommodation_id: Reference to Accommodation (FK, NOT NULL)
* name: Room type name (NOT NULL)
* description: Room description
* max_guests: Maximum occupancy (NOT NULL)
* beds_description: Description of bed configuration
* size_sqm: Room size in square meters
* image: Room image URL
* created_at, updated_at: Timestamps

### AccommodationRate
* id: Unique identifier (PK)
* accommodation_id: Reference to Accommodation (FK, NOT NULL)
* room_id: Reference to AccommodationRoom (FK, NOT NULL)
* date_from: Start date for rate (NOT NULL)
* date_to: End date for rate (NOT NULL)
* base_price: Nightly rate (NOT NULL)
* currency: Currency code (NOT NULL)
* breakfast_included: Boolean (NOT NULL, DEFAULT false)
* refundable: Boolean (NOT NULL, DEFAULT true)
* cancellation_policy: Text description
* availability: Number of rooms available
* created_at, updated_at: Timestamps

### TripAccommodation
* id: Unique identifier (PK)
* trip_id: Reference to Trip (FK, NOT NULL)
* trip_day_id: Reference to TripDay (FK, NOT NULL)
* accommodation_id: Reference to Accommodation (FK, NOT NULL)
* room_id: Reference to AccommodationRoom (FK, NOT NULL)
* check_in_date: Arrival date (NOT NULL)
* check_out_date: Departure date (NOT NULL)
* nights: Number of nights (NOT NULL)
* guests: Number of guests (NOT NULL, DEFAULT 1)
* price_per_night: Confirmed rate (NOT NULL)
* total_price: Total accommodation cost (NOT NULL)
* currency: Currency code (NOT NULL)
* booking_reference: Reservation number
* booking_status: "confirmed", "pending", "cancelled" (NOT NULL, DEFAULT "pending")
* special_requests: Any special requests
* notes: Additional notes
* created_at, updated_at: Timestamps
* UNIQUE (trip_id, trip_day_id, accommodation_id, room_id)

## Activity Entities

### ActivityCategory
* id: Unique identifier (PK)
* name: Category name (Cultural, Nature, Adventure, etc.) (UNIQUE, NOT NULL)
* description: Brief description
* icon_id: Reference to Icon (FK)
* created_at, updated_at: Timestamps

### Activity
* id: Unique identifier (PK)
* name: Activity name (NOT NULL)
* location_id: Reference to Location (FK, NOT NULL)
* category_id: Reference to ActivityCategory (FK, NOT NULL)
* description: Detailed description (NOT NULL)
* duration_minutes: Typical duration (NOT NULL)
* price_adult: Regular adult price
* price_child: Child price
* currency: Currency code
* website: Official website
* phone: Contact number
* created_at, updated_at: Timestamps

### ActivityImage
* id: Unique identifier (PK)
* activity_id: Reference to Activity (FK, NOT NULL)
* image_url: Image URL (NOT NULL)
* caption: Image caption
* is_featured: Whether image should be featured (DEFAULT false)
* sequence: Display order (DEFAULT 0)
* created_at, updated_at: Timestamps

### ActivityOpeningHours
* id: Unique identifier (PK)
* activity_id: Reference to Activity (FK, NOT NULL)
* day_of_week: Day (0-6, 0=Sunday) (NOT NULL)
* opening_time: Opening time
* closing_time: Closing time
* is_closed: Whether activity is closed on this day (DEFAULT false)
* created_at, updated_at: Timestamps
* UNIQUE (activity_id, day_of_week)

### Tag
* id: Unique identifier (PK)
* name: Tag name (UNIQUE, NOT NULL)
* category: Tag category
* icon_id: Reference to Icon (FK)
* created_at, updated_at: Timestamps

### ActivityTagJunction
* id: Unique identifier (PK)
* activity_id: Reference to Activity (FK, NOT NULL)
* tag_id: Reference to Tag (FK, NOT NULL)
* created_at, updated_at: Timestamps
* UNIQUE (activity_id, tag_id)

### TripActivity
* id: Unique identifier (PK)
* trip_day_id: Reference to TripDay (FK, NOT NULL)
* activity_id: Reference to Activity (FK, NOT NULL)
* start_time: Scheduled start time (NOT NULL)
* end_time: Scheduled end time (NOT NULL)
* description: Personalized description
* total_price: Total activity cost
* currency: Currency code
* booking_status: "confirmed", "pending", "recommended" (NOT NULL, DEFAULT "recommended")
* booking_reference: Reservation number
* meeting_point: Description or coordinates
* notes: Additional notes
* sequence: Order in the day's plan (NOT NULL)
* created_at, updated_at: Timestamps

## Transportation Entities

### TransportMode
* id: Unique identifier (PK)
* name: Mode name (e.g., "train", "tram", "bus", "busway", "taxi", "rental_car", "flight") (UNIQUE, NOT NULL)
* icon_id: Reference to Icon (FK)
* description: Brief description
* is_public: Boolean (NOT NULL, DEFAULT true)
* is_local: Boolean (NOT NULL, DEFAULT true)
* allows_free_transfers: Boolean (NOT NULL, DEFAULT false)
* created_at, updated_at: Timestamps

### TransportProvider
* id: Unique identifier (PK)
* name: Provider name (UNIQUE, NOT NULL)
* website: Official website
* contact_phone: Contact number
* logo_url: URL to provider logo
* created_at, updated_at: Timestamps

### Station
* id: Unique identifier (PK)
* name: Station name (NOT NULL)
* location_id: Reference to Location (FK, NOT NULL)
* station_type: "airport", "train", "bus", "tram", "busway", "metro", "port", "multi" (NOT NULL)
* is_transfer_point: Boolean (NOT NULL, DEFAULT false)
* iata_code: For airports (UNIQUE)
* created_at, updated_at: Timestamps
* UNIQUE (name, location_id, station_type)

### TransportLine
* id: Unique identifier (PK)
* provider_id: Reference to TransportProvider (FK, NOT NULL)
* mode_id: Reference to TransportMode (FK, NOT NULL)
* line_number: Line identifier (NOT NULL)
* name: Line name
* city_id: Reference to City (FK) - For local transit only
* is_intercity: Boolean (NOT NULL, DEFAULT false)
* train_type: For trains only ("boraq", "tnr", "tnl")
* color: Line color (for display)
* fare_type: "fixed", "distance_based", "zone_based" (NOT NULL, DEFAULT "fixed")
* fare_amount: Fixed fare amount (for fixed fare lines)
* currency: Currency code
* created_at, updated_at: Timestamps
* UNIQUE (provider_id, line_number, city_id)

### LineStationJunction
* id: Unique identifier (PK)
* line_id: Reference to TransportLine (FK, NOT NULL)
* station_id: Reference to Station (FK, NOT NULL)
* sequence: Order in the route (NOT NULL)
* is_terminus: Boolean (NOT NULL, DEFAULT false)
* platform: Platform number/name
* travel_time_to_next: Minutes to next station
* distance_to_next: Distance to next station (km)
* created_at, updated_at: Timestamps
* UNIQUE (line_id, station_id, sequence)

### StationTransfer
* id: Unique identifier (PK)
* from_station_id: Reference to Station (FK, NOT NULL)
* to_station_id: Reference to Station (FK, NOT NULL)
* transfer_time_minutes: Typical transfer time (NOT NULL)
* transfer_type: "walking", "shuttle", "connection" (NOT NULL, DEFAULT "walking")
* is_free: Boolean (NOT NULL, DEFAULT true)
* transfer_instructions: Description
* created_at, updated_at: Timestamps
* UNIQUE (from_station_id, to_station_id)
* CHECK (from_station_id != to_station_id)

### TransportFare
* id: Unique identifier (PK)
* mode_id: Reference to TransportMode (FK, NOT NULL)
* provider_id: Reference to TransportProvider (FK)
* from_station_id: Reference to Station (FK)
* to_station_id: Reference to Station (FK)
* city_id: Reference to City (FK) - For local transit only
* line_id: Reference to TransportLine (FK)
* service_class: "economy", "first", "business", etc.
* price: Cost amount (NOT NULL)
* currency: Currency code (NOT NULL)
* valid_minutes: Duration validity (for local transit)
* duration_minutes: Typical journey time
* distance_km: Distance in kilometers
* created_at, updated_at: Timestamps

### CarRental
* id: Unique identifier (PK)
* provider_id: Reference to TransportProvider (FK, NOT NULL)
* city_id: Reference to City (FK, NOT NULL)
* location_id: Reference to Location (FK, NOT NULL)
* car_type: Vehicle type (NOT NULL)
* daily_rate: Daily rental cost (NOT NULL)
* currency: Currency code (NOT NULL)
* insurance_cost: Daily insurance cost
* is_airport_pickup: Boolean (NOT NULL, DEFAULT false)
* created_at, updated_at: Timestamps

### TaxiFare
* id: Unique identifier (PK)
* city_id: Reference to City (FK, NOT NULL)
* base_fare: Starting fare (NOT NULL)
* per_km_rate: Per kilometer rate (NOT NULL)
* per_minute_rate: Per minute rate
* currency: Currency code (NOT NULL)
* provider_id: Reference to TransportProvider (FK)
* created_at, updated_at: Timestamps

### TripTransport
* id: Unique identifier (PK)
* trip_day_id: Reference to TripDay (FK, NOT NULL)
* mode_id: Reference to TransportMode (FK, NOT NULL)
* from_location_id: Reference to Location (origin) (FK, NOT NULL)
* to_location_id: Reference to Location (destination) (FK, NOT NULL)
* departure_time: Scheduled departure (NOT NULL)
* arrival_time: Scheduled arrival (NOT NULL)
* provider_id: Reference to TransportProvider (FK)
* line_id: Reference to TransportLine (FK)
* fare_id: Reference to TransportFare (FK)
* price: Total cost (NOT NULL)
* currency: Currency code (NOT NULL)
* booking_reference: Reservation number
* status: "confirmed", "pending", "recommended" (NOT NULL, DEFAULT "recommended")
* sequence: Order in the day's plan (NOT NULL)
* created_at, updated_at: Timestamps

## Food & Dining Entities

### CuisineType
* id: Unique identifier (PK)
* name: Cuisine name (UNIQUE, NOT NULL)
* description: Brief description
* icon_id: Reference to Icon (FK)
* created_at, updated_at: Timestamps

### RestaurantType
* id: Unique identifier (PK)
* name: Type name (Casual, Fine Dining, Street Food, etc.) (UNIQUE, NOT NULL)
* description: Brief description
* icon_id: Reference to Icon (FK)
* created_at, updated_at: Timestamps

### Restaurant
* id: Unique identifier (PK)
* name: Restaurant name (NOT NULL)
* location_id: Reference to Location (FK, NOT NULL)
* type_id: Reference to RestaurantType (FK, NOT NULL)
* price_range: Budget indicator (1-4) (NOT NULL)
* rating: Average rating (0-5)
* description: Restaurant description
* website: Official website
* phone: Contact number
* menu_url: Link to menu
* created_at, updated_at: Timestamps

### RestaurantCuisineJunction
* id: Unique identifier (PK)
* restaurant_id: Reference to Restaurant (FK, NOT NULL)
* cuisine_id: Reference to CuisineType (FK, NOT NULL)
* created_at, updated_at: Timestamps
* UNIQUE (restaurant_id, cuisine_id)

### RestaurantImage
* id: Unique identifier (PK)
* restaurant_id: Reference to Restaurant (FK, NOT NULL)
* image_url: Image URL (NOT NULL)
* caption: Image caption
* is_featured: Whether image should be featured (DEFAULT false)
* sequence: Display order (DEFAULT 0)
* created_at, updated_at: Timestamps

### RestaurantOpeningHours
* id: Unique identifier (PK)
* restaurant_id: Reference to Restaurant (FK, NOT NULL)
* day_of_week: Day (0-6, 0=Sunday) (NOT NULL)
* opening_time: Opening time
* closing_time: Closing time
* is_closed: Whether restaurant is closed on this day (DEFAULT false)
* created_at, updated_at: Timestamps
* UNIQUE (restaurant_id, day_of_week)

### MealType
* id: Unique identifier (PK)
* name: Type name (Breakfast, Lunch, Dinner, etc.) (UNIQUE, NOT NULL)
* icon_id: Reference to Icon (FK)
* typical_time_start: Typical earliest time (e.g., "07:00")
* typical_time_end: Typical latest time (e.g., "10:00")
* created_at, updated_at: Timestamps

### TripMeal
* id: Unique identifier (PK)
* trip_day_id: Reference to TripDay (FK, NOT NULL)
* meal_type_id: Reference to MealType (FK, NOT NULL)
* restaurant_id: Reference to Restaurant (FK, NOT NULL)
* time: Scheduled time (NOT NULL)
* duration_minutes: Expected duration (DEFAULT 60)
* description: Meal description
* price_per_person: Estimated cost per person
* total_price: Total meal cost
* currency: Currency code
* reservation_status: "confirmed", "recommended", "none" (NOT NULL, DEFAULT "none")
* reservation_time: If reserved
* reservation_reference: Booking reference
* notes: Additional notes
* sequence: Order in the day's plan (NOT NULL)
* created_at, updated_at: Timestamps

## Budget & Expense Entities

### BudgetCategory
* id: Unique identifier (PK)
* name: Category name (UNIQUE, NOT NULL)
* icon_id: Reference to Icon (FK)
* description: Brief description
* created_at, updated_at: Timestamps

#### BudgetAllocation
* id: Unique identifier (PK)
* trip_id: Reference to Trip (FK, NOT NULL)
* category_id: Reference to BudgetCategory (FK, NOT NULL)
* allocated_amount: Budgeted amount (NOT NULL)
* spent_amount: Actual spent (NOT NULL, DEFAULT 0)
* currency: Currency code (NOT NULL)
* UNIQUE (trip_id, category_id)

#### Expense
* id: Unique identifier (PK)
* trip_id: Reference to Trip (FK, NOT NULL)
* trip_day_id: Reference to TripDay (FK)
* category_id: Reference to BudgetCategory (FK, NOT NULL)
* amount: Expense amount (NOT NULL)
* currency: Currency code (NOT NULL)
* description: Expense description
* timestamp: Transaction time (NOT NULL, DEFAULT CURRENT_TIMESTAMP)
* payment_method: Payment method used

#### ExpenseAllocation
* expense_id: Reference to Expense (PK, FK)
* entity_type: "accommodation", "activity", "meal", "transport", "other" (PK, NOT NULL)
* entity_id: ID of related entity (PK)
* amount: Allocated amount (NOT NULL)

### Notification & Saved Items

#### Notification
* id: Unique identifier (PK)
* user_id: Reference to User (FK, NOT NULL)
* title: Notification title (NOT NULL)
* message: Notification body (NOT NULL)
* type: "message", "trip_update", "system", "promotion" (NOT NULL)
* sender_id: Reference to User (sender) (FK)
* related_entity_type: Entity type this relates to
* related_entity_id: ID of related entity
* timestamp: Creation timestamp (NOT NULL, DEFAULT CURRENT_TIMESTAMP)
* read: Whether read by user (NOT NULL, DEFAULT false)
* action_url: URL to navigate to

#### SavedItem
* id: Unique identifier (PK)
* user_id: Reference to User (FK, NOT NULL)
* entity_type: "city", "hotel", "restaurant", "activity", "attraction", "guided_tour" (NOT NULL)
* entity_id: ID of saved entity (NOT NULL)
* notes: User notes
* saved_at: Saved timestamp (NOT NULL, DEFAULT CURRENT_TIMESTAMP)
* UNIQUE (user_id, entity_type, entity_id)