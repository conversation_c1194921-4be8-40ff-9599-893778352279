-- ============================================================================
-- STEP 4: UPDATE REMAINING USER-RELATED FOREIGN KEYS
-- ============================================================================
-- Continues updating user-related foreign keys from previous file
-- This is split into multiple files to avoid overly long scripts

-- 5. UPDATE trip_change_log
-- Drop existing foreign key constraint
ALTER TABLE trip_change_log 
DROP CONSTRAINT IF EXISTS trip_change_log_user_id_fkey,
DROP CONSTRAINT IF EXISTS fk_trip_change_log_user;

-- Convert user_id column to UUID (keep id as BIGSERIAL)
ALTER TABLE trip_change_log 
ALTER COLUMN user_id TYPE UUID USING uuid_generate_v4();

-- Add new foreign key constraint to profile table
ALTER TABLE trip_change_log 
ADD CONSTRAINT fk_trip_change_log_profile 
FOREIGN KEY (user_id) REFERENCES profile(id) ON DELETE CASCADE;

-- 6. UPDATE guide_request (traveler_id)
-- Drop existing foreign key constraint for traveler_id
ALTER TABLE guide_request 
DROP CONSTRAINT IF EXISTS guide_request_traveler_id_fkey,
DROP CONSTRAINT IF EXISTS fk_guide_request_traveler;

-- Convert traveler_id column to UUID (keep id as BIGSERIAL)
ALTER TABLE guide_request 
ALTER COLUMN traveler_id TYPE UUID USING uuid_generate_v4();

-- Add new foreign key constraint to profile table
ALTER TABLE guide_request 
ADD CONSTRAINT fk_guide_request_traveler_profile 
FOREIGN KEY (traveler_id) REFERENCES profile(id) ON DELETE CASCADE;

-- 7. UPDATE notification (user_id and sender_id)
-- Drop existing foreign key constraints
ALTER TABLE notification 
DROP CONSTRAINT IF EXISTS notification_user_id_fkey,
DROP CONSTRAINT IF EXISTS notification_sender_id_fkey,
DROP CONSTRAINT IF EXISTS fk_notification_user,
DROP CONSTRAINT IF EXISTS fk_notification_sender;

-- Convert both user_id and sender_id columns to UUID (keep id as BIGSERIAL)
ALTER TABLE notification 
ALTER COLUMN user_id TYPE UUID USING uuid_generate_v4(),
ALTER COLUMN sender_id TYPE UUID USING uuid_generate_v4();

-- Add new foreign key constraints to profile table
ALTER TABLE notification 
ADD CONSTRAINT fk_notification_user_profile 
FOREIGN KEY (user_id) REFERENCES profile(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_notification_sender_profile 
FOREIGN KEY (sender_id) REFERENCES profile(id) ON DELETE SET NULL;

-- 8. UPDATE saved_item
-- Drop existing foreign key constraint
ALTER TABLE saved_item 
DROP CONSTRAINT IF EXISTS saved_item_user_id_fkey,
DROP CONSTRAINT IF EXISTS fk_saved_item_user;

-- Convert user_id column to UUID (keep id as BIGSERIAL)
ALTER TABLE saved_item 
ALTER COLUMN user_id TYPE UUID USING uuid_generate_v4();

-- Add new foreign key constraint to profile table
ALTER TABLE saved_item 
ADD CONSTRAINT fk_saved_item_profile 
FOREIGN KEY (user_id) REFERENCES profile(id) ON DELETE CASCADE;
