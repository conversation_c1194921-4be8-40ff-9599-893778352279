-- ============================================================================
-- STEP 7: VERIFICATION QUERIES
-- ============================================================================
-- Run these queries to verify the schema conversion was successful
-- These are SELECT queries only - safe to run multiple times

-- 1. Verify profile table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'profile'
    AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Verify UUID foreign key columns
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns
WHERE table_schema = 'public'
    AND column_name IN ('user_id', 'traveler_id', 'sender_id')
    AND data_type = 'uuid'
ORDER BY table_name, column_name;

-- 3. Verify BIGINT primary keys are preserved
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns
WHERE table_schema = 'public'
    AND column_name = 'id'
    AND table_name != 'profile'
    AND data_type = 'bigint'
ORDER BY table_name;

-- 4. Verify foreign key constraints to profile table
SELECT 
    tc.table_name,
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND ccu.table_name = 'profile'
ORDER BY tc.table_name, tc.constraint_name;

-- 5. Verify RLS is enabled on user-related tables
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables
WHERE schemaname = 'public'
    AND tablename IN (
        'profile', 'user_system_preference', 'user_interest_junction',
        'guide_profile', 'trip', 'trip_change_log', 'guide_request',
        'notification', 'saved_item'
    )
ORDER BY tablename;

-- 6. Count RLS policies created
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- 7. Verify sequences are still working for BIGSERIAL columns
SELECT 
    sequence_name,
    data_type,
    start_value,
    increment
FROM information_schema.sequences
WHERE sequence_schema = 'public'
ORDER BY sequence_name;

-- 8. Check for any remaining BIGINT user_id columns (should be none)
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns
WHERE table_schema = 'public'
    AND column_name IN ('user_id', 'traveler_id', 'sender_id')
    AND data_type = 'bigint'
ORDER BY table_name, column_name;
