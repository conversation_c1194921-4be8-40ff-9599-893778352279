-- ============================================================================
-- PHASE 8: VERIFICATION QUERIES
-- ============================================================================
-- Run these to verify the migration was successful

-- Count images by entity type
SELECT 
    entity_type,
    COUNT(*) as image_count,
    COUNT(DISTINCT entity_id) as entity_count
FROM entity_image 
GROUP BY entity_type
ORDER BY entity_type;

-- Check for featured images
SELECT 
    entity_type,
    COUNT(*) as featured_count
FROM entity_image 
WHERE is_featured = true
GROUP BY entity_type
ORDER BY entity_type;

-- Verify no duplicate featured images per entity
SELECT 
    entity_type,
    entity_id,
    COUNT(*) as featured_count
FROM entity_image 
WHERE is_featured = true
GROUP BY entity_type, entity_id
HAVING COUNT(*) > 1;

-- Check total image count
SELECT COUNT(*) as total_images FROM image;
SELECT COUNT(*) as total_entity_images FROM entity_image;

-- Sample query to get images for an accommodation
SELECT 
    i.url,
    ei.image_type,
    ei.caption,
    ei.is_featured,
    ei.sequence
FROM entity_image ei
JOIN image i ON ei.image_id = i.id
WHERE ei.entity_type = 'accommodation' 
    AND ei.entity_id = 1  -- Replace with actual accommodation ID
ORDER BY ei.sequence;

-- Check for orphaned images (images not referenced by any entity)
SELECT COUNT(*) as orphaned_images
FROM image i
LEFT JOIN entity_image ei ON i.id = ei.image_id
WHERE ei.image_id IS NULL;
