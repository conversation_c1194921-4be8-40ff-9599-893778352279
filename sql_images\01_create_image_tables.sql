-- ============================================================================
-- PHASE 1: CREATE NEW IMAGE MANAGEMENT TABLES
-- ============================================================================
-- Execute this first to create the new centralized image system

-- Central Image Table
CREATE TABLE image (
    id BIGSERIAL PRIMARY KEY,
    url TEXT NOT NULL,
    alt_text TEXT,
    file_size INTEGER,
    mime_type VARCHAR(50),
    width INTEGER,
    height INTEGER,
    storage_provider VARCHAR(50) DEFAULT 'local',
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Entity Image Junction Table
CREATE TABLE entity_image (
    id BIGSERIAL PRIMARY KEY,
    image_id BIGINT NOT NULL,
    entity_type VARCHAR(50) NOT NULL CHECK (entity_type IN ('accommodation', 'activity', 'restaurant', 'tour', 'city', 'user', 'trip', 'room', 'provider')),
    entity_id BIGINT NOT NULL,
    image_type VARCHAR(50) NOT NULL DEFAULT 'gallery' CHECK (image_type IN ('featured', 'gallery', 'thumbnail', 'hero', 'avatar', 'logo')),
    sequence INTEGER DEFAULT 0,
    caption TEXT,
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (image_id) REFERENCES image(id) ON DELETE CASCADE,
    UNIQUE (entity_type, entity_id, image_id)
);

-- Create indexes for performance
CREATE INDEX idx_entity_image_entity ON entity_image(entity_type, entity_id);
CREATE INDEX idx_entity_image_featured ON entity_image(entity_type, entity_id, is_featured) WHERE is_featured = true;
CREATE INDEX idx_entity_image_sequence ON entity_image(entity_type, entity_id, sequence);
CREATE INDEX idx_image_url ON image(url);

-- Add constraint to ensure only one featured image per entity
CREATE UNIQUE INDEX idx_entity_image_one_featured 
ON entity_image(entity_type, entity_id) 
WHERE is_featured = true;
