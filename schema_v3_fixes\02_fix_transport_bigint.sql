-- ============================================================================
-- PHASE 2: FIX TRANSPORT TABLES BIGINT CONVERSION
-- ============================================================================
-- Convert all transport-related tables to BIGINT

-- Fix transport core tables
ALTER TABLE transport_mode ALTER COLUMN id TYPE BIGINT;

ALTER TABLE transport_provider ALTER COLUMN id TYPE BIGINT;

ALTER TABLE transport_line ALTER COLUMN id TYPE BIGINT;

ALTER TABLE transport_fare ALTER COLUMN id TYPE BIGINT;

ALTER TABLE station ALTER COLUMN id TYPE BIGINT;

-- Fix transport junction tables
ALTER TABLE line_station_junction ALTER COLUMN id TYPE BIGINT;

ALTER TABLE station_transfer ALTER COLUMN id TYPE BIGINT;

-- Fix car rental and taxi fare
ALTER TABLE car_rental ALTER COLUMN id TYPE BIGINT;

ALTER TABLE taxi_fare ALTER COLUMN id TYPE BIGINT;

-- Fix other core tables
ALTER TABLE icon ALTER COLUMN id TYPE BIGINT;

ALTER TABLE language ALTER COLUMN id TYPE BIGINT;

ALTER TABLE interest_category ALTER COLUMN id TYPE BIGINT;
ALTER TABLE interest_category ALTER COLUMN icon_id TYPE BIGINT;

-- Fix user related tables
ALTER TABLE "user" ALTER COLUMN id TYPE BIGINT;

ALTER TABLE user_system_preference ALTER COLUMN id TYPE BIGINT;
ALTER TABLE user_system_preference ALTER COLUMN user_id TYPE BIGINT;

ALTER TABLE user_interest_junction ALTER COLUMN id TYPE BIGINT;
ALTER TABLE user_interest_junction ALTER COLUMN user_id TYPE BIGINT;
ALTER TABLE user_interest_junction ALTER COLUMN interest_id TYPE BIGINT;

-- Fix guide tables
ALTER TABLE guide_profile ALTER COLUMN id TYPE BIGINT;
ALTER TABLE guide_profile ALTER COLUMN user_id TYPE BIGINT;

ALTER TABLE guide_language_junction ALTER COLUMN id TYPE BIGINT;
ALTER TABLE guide_language_junction ALTER COLUMN guide_id TYPE BIGINT;
ALTER TABLE guide_language_junction ALTER COLUMN language_id TYPE BIGINT;

ALTER TABLE guide_availability ALTER COLUMN id TYPE BIGINT;
ALTER TABLE guide_availability ALTER COLUMN guide_id TYPE BIGINT;

ALTER TABLE guide_specialty ALTER COLUMN id TYPE BIGINT;
ALTER TABLE guide_specialty ALTER COLUMN guide_id TYPE BIGINT;
ALTER TABLE guide_specialty ALTER COLUMN icon_id TYPE BIGINT;

ALTER TABLE guide_location_junction ALTER COLUMN id TYPE BIGINT;
ALTER TABLE guide_location_junction ALTER COLUMN guide_id TYPE BIGINT;

ALTER TABLE guide_request ALTER COLUMN id TYPE BIGINT;
ALTER TABLE guide_request ALTER COLUMN traveler_id TYPE BIGINT;
ALTER TABLE guide_request ALTER COLUMN guide_id TYPE BIGINT;
ALTER TABLE guide_request ALTER COLUMN trip_id TYPE BIGINT;
ALTER TABLE guide_request ALTER COLUMN tour_id TYPE BIGINT;

ALTER TABLE guided_tour ALTER COLUMN id TYPE BIGINT;
ALTER TABLE guided_tour ALTER COLUMN guide_id TYPE BIGINT;
