-- ============================================================================
-- STEP 5B: LOCAL DEVELOPMENT RLS POLICIES (ALTERNATIVE TO 05)
-- ============================================================================
-- Use this instead of 05_create_basic_rls_policies.sql for local development
-- This creates RLS policies that work without Supabase's auth schema
-- Switch to the original file when deploying to Supabase

-- First, drop any existing policies that might have been created
DROP POLICY IF EXISTS "Users can view own profile" ON profile;
DROP POLICY IF EXISTS "Users can update own profile" ON profile;
DROP POLICY IF EXISTS "Anyone can view guide profiles" ON profile;
DROP POLICY IF EXISTS "Users can manage own preferences" ON user_system_preference;
DROP POLICY IF EXISTS "Users can manage own interests" ON user_interest_junction;
DROP POLICY IF EXISTS "Guides can manage own profile" ON guide_profile;
DROP POLICY IF EXISTS "Anyone can view guide profiles" ON guide_profile;
DROP POLICY IF EXISTS "Users can manage own trips" ON trip;
DROP POLICY IF EXISTS "Users can view own trip changes" ON trip_change_log;
DROP POLICY IF EXISTS "Travelers can manage sent invitations" ON guide_request;
DROP POLICY IF EXISTS "Guides can view received invitations" ON guide_request;
DROP POLICY IF EXISTS "Guides can update invitation status" ON guide_request;
DROP POLICY IF EXISTS "Users can view own notifications" ON notification;
DROP POLICY IF EXISTS "Users can send notifications" ON notification;
DROP POLICY IF EXISTS "Users can manage own saved items" ON saved_item;

-- Enable RLS on all user-related tables (if not already enabled)
ALTER TABLE user_system_preference ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_interest_junction ENABLE ROW LEVEL SECURITY;
ALTER TABLE guide_profile ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_change_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE guide_request ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_item ENABLE ROW LEVEL SECURITY;

-- TEMPORARY: Disable RLS for local development
-- This allows all operations while you develop locally
-- Re-enable when you deploy to Supabase

ALTER TABLE profile DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_system_preference DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_interest_junction DISABLE ROW LEVEL SECURITY;
ALTER TABLE guide_profile DISABLE ROW LEVEL SECURITY;
ALTER TABLE trip DISABLE ROW LEVEL SECURITY;
ALTER TABLE trip_change_log DISABLE ROW LEVEL SECURITY;
ALTER TABLE guide_request DISABLE ROW LEVEL SECURITY;
ALTER TABLE notification DISABLE ROW LEVEL SECURITY;
ALTER TABLE saved_item DISABLE ROW LEVEL SECURITY;

-- Create a mock auth schema and function for local testing (optional)
-- Uncomment these if you want to test RLS policies locally

/*
-- Create mock auth schema
CREATE SCHEMA IF NOT EXISTS auth;

-- Create mock auth.uid() function that returns a test UUID
CREATE OR REPLACE FUNCTION auth.uid()
RETURNS UUID AS $$
BEGIN
    -- Return a test UUID for local development
    -- You can change this to test different users
    RETURN '550e8400-e29b-41d4-a716-************'::UUID;
END;
$$ LANGUAGE plpgsql;

-- Re-enable RLS if you want to test with the mock function
-- ALTER TABLE profile ENABLE ROW LEVEL SECURITY;
-- Then create policies using auth.uid() as normal
*/
