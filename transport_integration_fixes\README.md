# Transport Schema Integration Fixes

This folder contains SQL files to integrate your isolated transportation schema with the rest of the database. Execute the files in the numbered order.

## Execution Order

### 1. `01_update_data_types.sql`
- Updates all foreign key columns to BIGINT for consistency
- Fixes data type mismatches between transport tables and referencing tables
- **CRITICAL**: Run this first to avoid foreign key constraint errors

### 2. `02_add_transport_foreign_keys.sql`
- Adds missing foreign key constraints within transport tables
- Links transport_mode to icon table
- Ensures transport_fare has proper relationships

### 3. `03_add_trip_transport_foreign_keys.sql`
- Links trip_transport table to transport infrastructure
- Establishes the critical connection between trip management and transport system
- Enables proper trip planning with transport options

### 4. `04_add_location_foreign_keys.sql`
- Completes location table integration across all domains
- Links accommodation, activity, and restaurant tables to location
- Ensures shared location infrastructure works properly

### 5. `05_verify_constraints.sql` (Optional)
- Verification queries to check all constraints are properly established
- Use these SELECT statements to confirm integration success
- Helps troubleshoot any issues

## Important Notes

- **Data Preservation**: All existing column names and structures are preserved
- **Location.city_id**: Remains nullable as requested (for stations outside cities)
- **BIGINT Consistency**: All ID fields will be BIGINT after execution
- **Safe Execution**: Each file can be run independently if needed

## Before Running

1. Backup your database
2. Ensure no active transactions are using the affected tables
3. Run during maintenance window if possible

## After Running

Your transport schema will be fully integrated with:
- Trip planning system
- Location infrastructure  
- Accommodation/activity/restaurant systems
- Proper referential integrity throughout
