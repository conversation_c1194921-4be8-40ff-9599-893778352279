-- ============================================================================
-- PHASE 7: ADD ACTIVITY AND RESTAURANT FOREIGN KEY CONSTRAINTS
-- ============================================================================
-- Add foreign key constraints for activity and restaurant domains

-- Activity foreign keys
ALTER TABLE activity 
ADD CONSTRAINT fk_activity_category 
FOREIGN KEY (category_id) REFERENCES activity_category(id);

ALTER TABLE activity_opening_hours 
ADD CONSTRAINT fk_activity_opening_hours_activity 
FOREIGN KEY (activity_id) REFERENCES activity(id) ON DELETE CASCADE;

ALTER TABLE activity_tag_junction 
ADD CONSTRAINT fk_activity_tag_junction_activity 
FOREIGN KEY (activity_id) REFERENCES activity(id) ON DELETE CASCADE;

ALTER TABLE activity_tag_junction 
ADD CONSTRAINT fk_activity_tag_junction_tag 
FOREIGN KEY (tag_id) REFERENCES tag(id);

-- Restaurant foreign keys
ALTER TABLE restaurant 
ADD CONSTRAINT fk_restaurant_type 
FOREIGN KEY (type_id) REFERENCES restaurant_type(id);

ALTER TABLE restaurant_cuisine_junction 
ADD CONSTRAINT fk_restaurant_cuisine_junction_restaurant 
FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE;

ALTER TABLE restaurant_cuisine_junction 
ADD CONSTRAINT fk_restaurant_cuisine_junction_cuisine 
FOREIGN KEY (cuisine_id) REFERENCES cuisine_type(id);

ALTER TABLE restaurant_amenity_junction 
ADD CONSTRAINT fk_restaurant_amenity_junction_restaurant 
FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE;

ALTER TABLE restaurant_amenity_junction 
ADD CONSTRAINT fk_restaurant_amenity_junction_amenity 
FOREIGN KEY (amenity_id) REFERENCES amenity(id);

ALTER TABLE restaurant_opening_hours 
ADD CONSTRAINT fk_restaurant_opening_hours_restaurant 
FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE;

-- Trip foreign keys
ALTER TABLE trip 
ADD CONSTRAINT fk_trip_user 
FOREIGN KEY (user_id) REFERENCES "user"(id);

ALTER TABLE trip_change_log 
ADD CONSTRAINT fk_trip_change_log_trip 
FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE;

ALTER TABLE trip_change_log 
ADD CONSTRAINT fk_trip_change_log_user 
FOREIGN KEY (user_id) REFERENCES "user"(id);

ALTER TABLE trip_interest_junction 
ADD CONSTRAINT fk_trip_interest_junction_trip 
FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE;

ALTER TABLE trip_interest_junction 
ADD CONSTRAINT fk_trip_interest_junction_interest 
FOREIGN KEY (interest_id) REFERENCES interest_category(id);

ALTER TABLE trip_day 
ADD CONSTRAINT fk_trip_day_trip 
FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE;

ALTER TABLE daily_itinerary 
ADD CONSTRAINT fk_daily_itinerary_trip_day 
FOREIGN KEY (trip_day_id) REFERENCES trip_day(id) ON DELETE CASCADE;

ALTER TABLE daily_itinerary_item 
ADD CONSTRAINT fk_daily_itinerary_item_daily_itinerary 
FOREIGN KEY (daily_itinerary_id) REFERENCES daily_itinerary(id) ON DELETE CASCADE;

-- Tour foreign keys
ALTER TABLE tour_highlight 
ADD CONSTRAINT fk_tour_highlight_tour 
FOREIGN KEY (tour_id) REFERENCES guided_tour(id) ON DELETE CASCADE;

ALTER TABLE tour_itinerary_point 
ADD CONSTRAINT fk_tour_itinerary_point_tour 
FOREIGN KEY (tour_id) REFERENCES guided_tour(id) ON DELETE CASCADE;

-- Trip booking foreign keys
ALTER TABLE trip_accommodation 
ADD CONSTRAINT fk_trip_accommodation_trip 
FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE;

ALTER TABLE trip_accommodation 
ADD CONSTRAINT fk_trip_accommodation_trip_day 
FOREIGN KEY (trip_day_id) REFERENCES trip_day(id) ON DELETE CASCADE;

ALTER TABLE trip_accommodation 
ADD CONSTRAINT fk_trip_accommodation_accommodation 
FOREIGN KEY (accommodation_id) REFERENCES accommodation(id);

ALTER TABLE trip_accommodation 
ADD CONSTRAINT fk_trip_accommodation_room 
FOREIGN KEY (room_id) REFERENCES accommodation_room(id);

ALTER TABLE trip_activity 
ADD CONSTRAINT fk_trip_activity_trip_day 
FOREIGN KEY (trip_day_id) REFERENCES trip_day(id) ON DELETE CASCADE;

ALTER TABLE trip_activity 
ADD CONSTRAINT fk_trip_activity_activity 
FOREIGN KEY (activity_id) REFERENCES activity(id);

ALTER TABLE trip_meal 
ADD CONSTRAINT fk_trip_meal_trip_day 
FOREIGN KEY (trip_day_id) REFERENCES trip_day(id) ON DELETE CASCADE;

ALTER TABLE trip_meal 
ADD CONSTRAINT fk_trip_meal_meal_type 
FOREIGN KEY (meal_type_id) REFERENCES meal_type(id);

ALTER TABLE trip_meal 
ADD CONSTRAINT fk_trip_meal_restaurant 
FOREIGN KEY (restaurant_id) REFERENCES restaurant(id);
