-- ============================================================================
-- PHASE 9: COMPLETE IMAGE SYSTEM MIGRATION
-- ============================================================================
-- Remove old image columns that should have been migrated to entity_image system

-- Remove old image columns from tables
-- These should have been migrated to the entity_image system

-- Remove hero_image from city (should use entity_image with type='hero')
ALTER TABLE city DROP COLUMN IF EXISTS hero_image;

-- Remove avatar_url from user (should use entity_image with type='avatar')
ALTER TABLE "user" DROP COLUMN IF EXISTS avatar_url;

-- Remove image_url from trip (should use entity_image with type='featured')
ALTER TABLE trip DROP COLUMN IF EXISTS image_url;

-- Remove logo_url from transport_provider (should use entity_image with type='logo')
ALTER TABLE transport_provider DROP COLUMN IF EXISTS logo_url;

-- Remove image from accommodation_room (should use entity_image with type='featured')
ALTER TABLE accommodation_room DROP COLUMN IF EXISTS image;

-- Verify entity_image table has proper constraints
-- Add missing constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'entity_image_image_id_fkey'
        AND table_name = 'entity_image'
    ) THEN
        ALTER TABLE entity_image 
        ADD CONSTRAINT fk_entity_image_image 
        FOREIGN KEY (image_id) REFERENCES image(id) ON DELETE CASCADE;
    END IF;
END $$;
