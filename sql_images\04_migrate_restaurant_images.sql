-- ============================================================================
-- PHASE 4: MIGRATE RESTAURANT IMAGES
-- ============================================================================
-- Migrate data from restaurant_image table to new system

-- Insert restaurant images into central image table
INSERT INTO image (url, alt_text, created_at, updated_at)
SELECT 
    image_url,
    caption,
    created_at,
    updated_at
FROM restaurant_image;

-- Create entity_image relationships for restaurants
INSERT INTO entity_image (image_id, entity_type, entity_id, image_type, sequence, caption, is_featured, created_at, updated_at)
SELECT 
    i.id,
    'restaurant',
    ri.restaurant_id,
    CASE WHEN ri.is_featured THEN 'featured' ELSE 'gallery' END,
    ri.sequence,
    ri.caption,
    ri.is_featured,
    ri.created_at,
    ri.updated_at
FROM restaurant_image ri
JOIN image i ON i.url = ri.image_url 
    AND i.created_at = ri.created_at;
