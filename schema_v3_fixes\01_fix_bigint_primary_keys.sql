-- ============================================================================
-- PHASE 1: FIX PRIMARY KEY DATA TYPES TO BIGINT
-- ============================================================================
-- CRITICAL: This fixes the core issue where many tables still use INTEGER for IDs

-- Fix accommodation table
ALTER TABLE accommodation ALTER COLUMN id TYPE BIGINT;
ALTER TABLE accommodation ALTER COLUMN type_id TYPE BIGINT;

-- Fix accommodation related tables
ALTER TABLE accommodation_amenity_junction ALTER COLUMN id TYPE BIGINT;
ALTER TABLE accommodation_amenity_junction ALTER COLUMN accommodation_id TYPE BIGINT;
ALTER TABLE accommodation_amenity_junction ALTER COLUMN amenity_id TYPE BIGINT;

ALTER TABLE accommodation_rate ALTER COLUMN id TYPE BIGINT;
ALTER TABLE accommodation_rate ALTER COLUMN accommodation_id TYPE BIGINT;
ALTER TABLE accommodation_rate ALTER COLUMN room_id TYPE BIGINT;

ALTER TABLE accommodation_room ALTER COLUMN id TYPE BIGINT;
ALTER TABLE accommodation_room ALTER COLUMN accommodation_id TYPE BIGINT;

ALTER TABLE accommodation_type ALTER COLUMN id TYPE BIGINT;
ALTER TABLE accommodation_type ALTER COLUMN icon_id TYPE BIGINT;

-- Fix activity tables
ALTER TABLE activity ALTER COLUMN id TYPE BIGINT;
ALTER TABLE activity ALTER COLUMN category_id TYPE BIGINT;

ALTER TABLE activity_category ALTER COLUMN id TYPE BIGINT;
ALTER TABLE activity_category ALTER COLUMN icon_id TYPE BIGINT;

ALTER TABLE activity_opening_hours ALTER COLUMN id TYPE BIGINT;
ALTER TABLE activity_opening_hours ALTER COLUMN activity_id TYPE BIGINT;

ALTER TABLE activity_tag_junction ALTER COLUMN id TYPE BIGINT;
ALTER TABLE activity_tag_junction ALTER COLUMN activity_id TYPE BIGINT;
ALTER TABLE activity_tag_junction ALTER COLUMN tag_id TYPE BIGINT;

-- Fix amenity table
ALTER TABLE amenity ALTER COLUMN id TYPE BIGINT;
ALTER TABLE amenity ALTER COLUMN icon_id TYPE BIGINT;

-- Fix budget tables
ALTER TABLE budget_allocation ALTER COLUMN id TYPE BIGINT;
ALTER TABLE budget_allocation ALTER COLUMN trip_id TYPE BIGINT;
ALTER TABLE budget_allocation ALTER COLUMN category_id TYPE BIGINT;

ALTER TABLE budget_category ALTER COLUMN id TYPE BIGINT;
ALTER TABLE budget_category ALTER COLUMN icon_id TYPE BIGINT;

-- Fix location and city tables
ALTER TABLE location ALTER COLUMN id TYPE BIGINT;

ALTER TABLE city ALTER COLUMN id TYPE BIGINT;
ALTER TABLE city ALTER COLUMN country_id TYPE BIGINT;

ALTER TABLE country ALTER COLUMN id TYPE BIGINT;

ALTER TABLE country_emergency_contact ALTER COLUMN id TYPE BIGINT;
ALTER TABLE country_emergency_contact ALTER COLUMN country_id TYPE BIGINT;

ALTER TABLE country_custom ALTER COLUMN id TYPE BIGINT;
ALTER TABLE country_custom ALTER COLUMN country_id TYPE BIGINT;
ALTER TABLE country_custom ALTER COLUMN icon_id TYPE BIGINT;
