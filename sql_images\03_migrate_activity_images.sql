-- ============================================================================
-- PHASE 3: MIGRATE ACTIVITY IMAGES
-- ============================================================================
-- Migrate data from activity_image table to new system

-- Insert activity images into central image table
INSERT INTO image (url, alt_text, created_at, updated_at)
SELECT 
    image_url,
    caption,
    created_at,
    updated_at
FROM activity_image;

-- Create entity_image relationships for activities
INSERT INTO entity_image (image_id, entity_type, entity_id, image_type, sequence, caption, is_featured, created_at, updated_at)
SELECT 
    i.id,
    'activity',
    ai.activity_id,
    CASE WHEN ai.is_featured THEN 'featured' ELSE 'gallery' END,
    ai.sequence,
    ai.caption,
    ai.is_featured,
    ai.created_at,
    ai.updated_at
FROM activity_image ai
JOIN image i ON i.url = ai.image_url 
    AND i.created_at = ai.created_at;
