-- ============================================================================
-- PHASE 2: MIGRATE ACCOMMODATION IMAGES
-- ============================================================================
-- Migrate data from accommodation_image table to new system

-- Insert accommodation images into central image table and create relationships
INSERT INTO image (url, alt_text, created_at, updated_at)
SELECT 
    image_url,
    caption,
    created_at,
    updated_at
FROM accommodation_image;

-- Create entity_image relationships for accommodations
INSERT INTO entity_image (image_id, entity_type, entity_id, image_type, sequence, caption, is_featured, created_at, updated_at)
SELECT 
    i.id,
    'accommodation',
    ai.accommodation_id,
    CASE WHEN ai.is_featured THEN 'featured' ELSE 'gallery' END,
    ai.sequence,
    ai.caption,
    ai.is_featured,
    ai.created_at,
    ai.updated_at
FROM accommodation_image ai
JOIN image i ON i.url = ai.image_url 
    AND i.created_at = ai.created_at;

-- Migrate accommodation room images (single image field)
INSERT INTO image (url, alt_text, created_at, updated_at)
SELECT 
    image,
    'Room image',
    created_at,
    updated_at
FROM accommodation_room 
WHERE image IS NOT NULL AND image != '';

-- Create entity_image relationships for rooms
INSERT INTO entity_image (image_id, entity_type, entity_id, image_type, sequence, caption, is_featured, created_at, updated_at)
SELECT 
    i.id,
    'room',
    ar.id,
    'featured',
    0,
    'Room image',
    true,
    ar.created_at,
    ar.updated_at
FROM accommodation_room ar
JOIN image i ON i.url = ar.image 
    AND i.created_at = ar.created_at
WHERE ar.image IS NOT NULL AND ar.image != '';
