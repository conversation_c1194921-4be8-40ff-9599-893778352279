-- ============================================================================
-- SCHEMA EDITED V2 - PART 2 (CONTINUATION)
-- ============================================================================
-- This is the continuation of schema_edited.sql
-- Execute schema_edited.sql first, then this file

-- ============================================================================
-- ACTIVITY ENTITIES
-- ============================================================================

-- Activity Category
CREATE TABLE activity_category (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon_id BIGINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Activity
CREATE TABLE activity (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    location_id BIGINT NOT NULL,
    category_id BIGINT NOT NULL,
    description TEXT NOT NULL,
    duration_minutes INTEGER NOT NULL,
    price_adult NUMERIC(10,2),
    price_child NUMERIC(10,2),
    currency CHAR(3),
    website TEXT,
    phone VARCHAR(20),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (location_id) REFERENCES location(id),
    FOREIGN KEY (category_id) REFERENCES activity_category(id)
);

-- Activity Opening Hours
CREATE TABLE activity_opening_hours (
    id BIGSERIAL PRIMARY KEY,
    activity_id BIGINT NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    opening_time TIME,
    closing_time TIME,
    is_closed BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (activity_id) REFERENCES activity(id) ON DELETE CASCADE,
    UNIQUE (activity_id, day_of_week)
);

-- Tag
CREATE TABLE tag (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    category VARCHAR(50),
    icon_id BIGINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Activity Tag Junction
CREATE TABLE activity_tag_junction (
    id BIGSERIAL PRIMARY KEY,
    activity_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (activity_id) REFERENCES activity(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tag(id),
    UNIQUE (activity_id, tag_id)
);

-- ============================================================================
-- FOOD & DINING ENTITIES
-- ============================================================================

-- Cuisine Type
CREATE TABLE cuisine_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    icon_id BIGINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Restaurant Type
CREATE TABLE restaurant_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    icon_id BIGINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Restaurant
CREATE TABLE restaurant (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    location_id BIGINT NOT NULL,
    type_id BIGINT NOT NULL,
    price_range INTEGER NOT NULL CHECK (price_range >= 1 AND price_range <= 4),
    rating NUMERIC(2,1) CHECK (rating >= 0 AND rating <= 5),
    description TEXT,
    website TEXT,
    phone VARCHAR(20),
    menu_url TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (location_id) REFERENCES location(id),
    FOREIGN KEY (type_id) REFERENCES restaurant_type(id)
);

-- Restaurant Cuisine Junction
CREATE TABLE restaurant_cuisine_junction (
    id BIGSERIAL PRIMARY KEY,
    restaurant_id BIGINT NOT NULL,
    cuisine_id BIGINT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE,
    FOREIGN KEY (cuisine_id) REFERENCES cuisine_type(id),
    UNIQUE (restaurant_id, cuisine_id)
);

-- Restaurant Amenity Junction
CREATE TABLE restaurant_amenity_junction (
    id BIGSERIAL PRIMARY KEY,
    restaurant_id BIGINT NOT NULL,
    amenity_id BIGINT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE,
    FOREIGN KEY (amenity_id) REFERENCES amenity(id),
    UNIQUE (restaurant_id, amenity_id)
);

-- Restaurant Opening Hours
CREATE TABLE restaurant_opening_hours (
    id BIGSERIAL PRIMARY KEY,
    restaurant_id BIGINT NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    opening_time TIME,
    closing_time TIME,
    is_closed BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE,
    UNIQUE (restaurant_id, day_of_week)
);

-- Meal Type
CREATE TABLE meal_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    icon_id BIGINT,
    typical_time_start TIME,
    typical_time_end TIME,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);
