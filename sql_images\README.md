# Image Consolidation Migration

This folder contains SQL scripts to consolidate all image-related tables and columns into a centralized image management system.

## Table Count Reduction

**BEFORE (Current Schema):**
- `accommodation_image` table
- `activity_image` table  
- `restaurant_image` table
- `tour_image` table
- `city.hero_image` column
- `user.avatar_url` column
- `trip.image_url` column
- `accommodation_room.image` column
- `transport_provider.logo_url` column

**Total: 4 dedicated image tables + 5 image columns = 9 image storage locations**

**AFTER (Consolidated Schema):**
- `image` table (central storage)
- `entity_image` table (relationships)

**Total: 2 tables**

**REDUCTION: From 9 storage locations to 2 tables (78% reduction in image-related structures)**

## Execution Order

### 1. `01_create_image_tables.sql`
- Creates the new `image` and `entity_image` tables
- Adds necessary indexes for performance
- Sets up constraints for data integrity

### 2. `02_migrate_accommodation_images.sql`
- Migrates `accommodation_image` table data
- Migrates `accommodation_room.image` column data
- Preserves featured flags and sequences

### 3. `03_migrate_activity_images.sql`
- Migrates `activity_image` table data
- Preserves all metadata (captions, featured status, sequence)

### 4. `04_migrate_restaurant_images.sql`
- Migrates `restaurant_image` table data
- Maintains image ordering and featured images

### 5. `05_migrate_tour_images.sql`
- Migrates `tour_image` table data
- Preserves tour-specific image metadata

### 6. `06_migrate_other_images.sql`
- Migrates single image fields (hero images, avatars, logos)
- Converts single images to proper entity relationships

### 7. `07_remove_old_image_columns.sql`
- **WARNING: DESTRUCTIVE** - Removes old tables and columns
- Only run after verifying migration success
- Creates clean schema without redundancy

### 8. `08_verification_queries.sql`
- Verification queries to check migration success
- Count queries to ensure no data loss
- Sample queries for testing new structure

## Benefits After Migration

✅ **Eliminated Redundancy:** Single source of truth for all images
✅ **Consistent Metadata:** Unified caption, sequence, and featured flag handling  
✅ **Better Performance:** Centralized indexing and query optimization
✅ **Easier Maintenance:** One place to manage image storage and metadata
✅ **Flexible Relationships:** Can easily add new entity types without new tables
✅ **Storage Optimization:** Eliminates duplicate image URLs across tables

## Important Notes

1. **Backup First:** Always backup your database before running these scripts
2. **Test Environment:** Run in test environment first
3. **Verification:** Use verification queries after each step
4. **Rollback Plan:** Keep old tables until verification is complete
5. **Application Updates:** Update application code to use new image structure

## New Query Patterns

```sql
-- Get all images for an accommodation
SELECT i.url, ei.image_type, ei.caption, ei.is_featured
FROM entity_image ei
JOIN image i ON ei.image_id = i.id
WHERE ei.entity_type = 'accommodation' AND ei.entity_id = ?
ORDER BY ei.sequence;

-- Get featured image for any entity
SELECT i.url, ei.caption
FROM entity_image ei
JOIN image i ON ei.image_id = i.id
WHERE ei.entity_type = ? AND ei.entity_id = ? AND ei.is_featured = true;
```
