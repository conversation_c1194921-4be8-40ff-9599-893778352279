# Supabase Schema Fixes

This folder contains SQL files to properly restructure your V4_schema.sql for Supabase authentication integration.

## 🎯 What This Does

**CORRECT APPROACH:**
- ✅ Only `profile.id` is UUID (matches Supabase auth.users.id)
- ✅ All other primary keys remain BIGSERIAL (auto-incrementing)
- ✅ Only user-related foreign keys converted to UUID
- ✅ Preserves all existing relationships and functionality

**FIXES PREVIOUS ERRORS:**
- ❌ No more "column is of type bigint but default expression is of type uuid" errors
- ❌ No more primary key conversion issues
- ❌ No more sequence conflicts

## 📋 Execution Order (CRITICAL)

### 1. `01_enable_uuid_extension.sql`
- Enables UUID generation functions
- Required before any UUID operations

### 2. `02_create_profile_table.sql`
- Replaces `user` table with Supabase-compatible `profile` table
- `profile.id` is UUID (no default, set by application)
- Removes authentication fields (email, password_hash)
- Enables Row Level Security

### 3. `03_update_user_foreign_keys.sql`
- Converts user-related foreign keys to UUID:
  - `user_system_preference.user_id`
  - `user_interest_junction.user_id`
  - `guide_profile.user_id`
  - `trip.user_id`
- **Keeps all primary keys as BIGSERIAL**

### 4. `04_update_remaining_user_foreign_keys.sql`
- Continues UUID conversion for remaining tables:
  - `trip_change_log.user_id`
  - `guide_request.traveler_id`
  - `notification.user_id` and `notification.sender_id`
  - `saved_item.user_id`

### 5. `05_create_basic_rls_policies.sql`
- Sets up Row Level Security policies
- **Travelers:** See own data + public guide info
- **Guides:** See dashboard + received invitations
- **No cross-user access**

### 6. `06_create_auth_trigger.sql`
- Creates automatic profile creation on user signup
- Handles Supabase auth.users → profile relationship
- Includes error handling

### 7. `07_verification_queries.sql`
- Verification queries to confirm success
- Check UUID conversions
- Verify BIGSERIAL preservation
- Confirm RLS setup

## 🔍 Key Differences from Previous Approach

### ✅ CORRECT (This Approach)
```sql
-- profile table: UUID primary key
profile.id UUID

-- Other tables: BIGSERIAL primary keys (auto-incrementing)
trip.id BIGSERIAL
guide_profile.id BIGSERIAL
accommodation.id BIGSERIAL

-- Only user-related foreign keys are UUID
trip.user_id UUID → references profile.id
guide_profile.user_id UUID → references profile.id

-- All other foreign keys remain BIGINT
trip.origin_airport_id BIGINT → references station.id
accommodation.location_id BIGINT → references location.id
```

### ❌ WRONG (Previous Approach)
```sql
-- Tried to convert ALL primary keys to UUID
trip.id UUID ← WRONG!
guide_profile.id UUID ← WRONG!

-- Caused sequence conflicts and auto-increment issues
```

## 🎯 Expected Results

**After running all files:**
- ✅ `profile` table with UUID primary key
- ✅ All other tables keep BIGSERIAL primary keys
- ✅ User-related foreign keys are UUID
- ✅ All other foreign keys remain BIGINT
- ✅ RLS policies protect user data
- ✅ Auto profile creation on signup

## 🚀 Frontend Integration

```javascript
// Registration with automatic profile creation
const { data: authData, error: authError } = await supabase.auth.signUp({
    email: email,
    password: password,
    options: {
        data: {
            name: name,
            role: selectedRole // 'traveler' or 'guide'
        }
    }
});

// Profile is automatically created by trigger
// authData.user.id is UUID that matches profile.id
```

## ⚠️ Important Notes

1. **Run in exact order** - each file depends on previous ones
2. **profile.id has no default** - must be set by application to auth.user.id
3. **All other IDs auto-increment** - no manual ID setting needed
4. **RLS policies are basic** - can be enhanced later
5. **Backup before running** - these make structural changes

This approach maintains your existing schema design while making it Supabase-compatible with minimal changes.
