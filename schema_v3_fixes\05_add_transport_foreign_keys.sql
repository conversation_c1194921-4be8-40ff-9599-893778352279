-- ============================================================================
-- PHASE 5: ADD TRANSPORT FOREIGN KEY CONSTRAINTS
-- ============================================================================
-- Add all transport-related foreign key constraints

-- Transport provider foreign keys
ALTER TABLE transport_line 
ADD CONSTRAINT fk_transport_line_provider 
FOREIGN KEY (provider_id) REFERENCES transport_provider(id);

ALTER TABLE transport_fare 
ADD CONSTRAINT fk_transport_fare_provider 
FOREIGN KEY (provider_id) REFERENCES transport_provider(id);

ALTER TABLE car_rental 
ADD CONSTRAINT fk_car_rental_provider 
FOREIGN KEY (provider_id) REFERENCES transport_provider(id);

ALTER TABLE taxi_fare 
ADD CONSTRAINT fk_taxi_fare_provider 
FOREIGN KEY (provider_id) REFERENCES transport_provider(id);

-- Transport mode foreign keys
ALTER TABLE transport_line 
ADD CONSTRAINT fk_transport_line_mode 
FOREIGN KEY (mode_id) REFERENCES transport_mode(id);

ALTER TABLE transport_fare 
ADD CONSTRAINT fk_transport_fare_mode 
FOREIGN KEY (mode_id) REFERENCES transport_mode(id);

-- Transport line foreign keys
ALTER TABLE line_station_junction 
ADD CONSTRAINT fk_line_station_junction_line 
FOREIGN KEY (line_id) REFERENCES transport_line(id);

ALTER TABLE transport_fare 
ADD CONSTRAINT fk_transport_fare_line 
FOREIGN KEY (line_id) REFERENCES transport_line(id);

-- Station foreign keys
ALTER TABLE line_station_junction 
ADD CONSTRAINT fk_line_station_junction_station 
FOREIGN KEY (station_id) REFERENCES station(id);

ALTER TABLE station_transfer 
ADD CONSTRAINT fk_station_transfer_from_station 
FOREIGN KEY (from_station_id) REFERENCES station(id);

ALTER TABLE station_transfer 
ADD CONSTRAINT fk_station_transfer_to_station 
FOREIGN KEY (to_station_id) REFERENCES station(id);

ALTER TABLE transport_fare 
ADD CONSTRAINT fk_transport_fare_from_station 
FOREIGN KEY (from_station_id) REFERENCES station(id);

ALTER TABLE transport_fare 
ADD CONSTRAINT fk_transport_fare_to_station 
FOREIGN KEY (to_station_id) REFERENCES station(id);

-- Trip transport foreign keys (CRITICAL - these were missing)
ALTER TABLE trip_transport 
ADD CONSTRAINT fk_trip_transport_mode 
FOREIGN KEY (mode_id) REFERENCES transport_mode(id);

ALTER TABLE trip_transport 
ADD CONSTRAINT fk_trip_transport_provider 
FOREIGN KEY (provider_id) REFERENCES transport_provider(id);

ALTER TABLE trip_transport 
ADD CONSTRAINT fk_trip_transport_line 
FOREIGN KEY (line_id) REFERENCES transport_line(id);

ALTER TABLE trip_transport 
ADD CONSTRAINT fk_trip_transport_fare 
FOREIGN KEY (fare_id) REFERENCES transport_fare(id);

ALTER TABLE trip_transport 
ADD CONSTRAINT fk_trip_transport_from_location 
FOREIGN KEY (from_location_id) REFERENCES location(id);

ALTER TABLE trip_transport 
ADD CONSTRAINT fk_trip_transport_to_location 
FOREIGN KEY (to_location_id) REFERENCES location(id);

ALTER TABLE trip_transport 
ADD CONSTRAINT fk_trip_transport_trip_day 
FOREIGN KEY (trip_day_id) REFERENCES trip_day(id) ON DELETE CASCADE;

-- Trip origin airport foreign key
ALTER TABLE trip 
ADD CONSTRAINT fk_trip_origin_airport 
FOREIGN KEY (origin_airport_id) REFERENCES station(id);
