import psycopg2
from config import db_params


def create_connection(db_params):
    try:
        return psycopg2.connect(**db_params)
    except psycopg2.errors as e :
        print(f"error connecting to the database: {e}")
        return None

def create_table(conn, file_path):
    cur = conn.cursor()
    try:
        with open(file_path, 'r') as file:
            cur.execute(file.read())
            conn.commit()
    except psycopg2.errors as e:
        print(f'errors creating tables as: {e}')
        conn.rollback()
    finally:
        cur.close()

def main():
    conn = create_connection(db_params)
    if conn is not None:
        create_table(conn, 'schema.sql')
        conn.close()
        print("Database created successfully!")
    else:
        print("Error creating database.")

if __name__ == '__main__':
    main()