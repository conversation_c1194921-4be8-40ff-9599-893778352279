-- ============================================================================
-- PHASE 2: ADD MISSING FOREIGN KEY CONSTRAINTS FOR TRANSPORT TABLES
-- ============================================================================
-- Execute these after data type updates are complete

-- Add foreign key constraint for transport_mode.icon_id
ALTER TABLE transport_mode 
ADD CONSTRAINT fk_transport_mode_icon 
FOREIGN KEY (icon_id) REFERENCES icon(id);

-- Add foreign key constraints for transport_fare table (missing ones)
ALTER TABLE transport_fare 
ADD CONSTRAINT fk_transport_fare_line 
FOREIGN KEY (line_id) REFERENCES transport_line(id);

-- Verify and add missing constraints for car_rental if needed
-- (car_rental already has proper BIGINT types, just ensuring FK exists)
ALTER TABLE car_rental 
ADD CONSTRAINT fk_car_rental_provider 
FOREIGN KEY (provider_id) REFERENCES transport_provider(id);

ALTER TABLE car_rental 
ADD CONSTRAINT fk_car_rental_city 
FOREIGN KEY (city_id) REFERENCES city(id);

ALTER TABLE car_rental 
ADD CONSTRAINT fk_car_rental_location 
FOREIGN KEY (location_id) REFERENCES location(id);

-- Add missing constraint for taxi_fare
ALTER TABLE taxi_fare 
ADD CONSTRAINT fk_taxi_fare_city 
FOREIGN KEY (city_id) REFERENCES city(id);

ALTER TABLE taxi_fare 
ADD CONSTRAINT fk_taxi_fare_provider 
FOREIGN KEY (provider_id) REFERENCES transport_provider(id);
