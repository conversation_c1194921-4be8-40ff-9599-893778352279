-- ============================================================================
-- STEP 2: CREATE PROFILE TABLE (REPLACE USER TABLE)
-- ============================================================================
-- This replaces the user table with a Supabase-compatible profile table
-- - profile.id is UUID (matches Supabase auth.users.id)
-- - Removes authentication fields (email, password_hash)
-- - Keeps application-specific user data

-- Drop existing user table and all its constraints
DROP TABLE IF EXISTS "user" CASCADE;

-- Create new profile table with UUID primary key
CREATE TABLE profile (
    id UUID PRIMARY KEY, -- Will be set to auth.users.id, no default needed
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role VARCHAR(20) NOT NULL DEFAULT 'traveler' 
        CHECK (role IN ('traveler', 'guide', 'admin', 'both')),
    status VARCHAR(20) NOT NULL DEFAULT 'active' 
        CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add unique constraint on phone if not null
ALTER TABLE profile ADD CONSTRAINT profile_phone_unique UNIQUE (phone);

-- Enable Row Level Security
ALTER TABLE profile ENABLE ROW LEVEL SECURITY;

-- Verify table creation
\d profile;
