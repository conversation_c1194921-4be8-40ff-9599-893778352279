-- ============================================================================
-- PHASE 8: ADD BUDGET AND EXPENSE FOREIGN KEY CONSTRAINTS
-- ============================================================================
-- Add foreign key constraints for budget and expense management

-- Budget foreign keys
ALTER TABLE budget_allocation 
ADD CONSTRAINT fk_budget_allocation_trip 
FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE;

ALTER TABLE budget_allocation 
ADD CONSTRAINT fk_budget_allocation_category 
FOREIGN KEY (category_id) REFERENCES budget_category(id);

-- Expense foreign keys
ALTER TABLE expense 
ADD CONSTRAINT fk_expense_trip 
FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE;

ALTER TABLE expense 
ADD CONSTRAINT fk_expense_trip_day 
FOREIGN KEY (trip_day_id) REFERENCES trip_day(id) ON DELETE CASCADE;

ALTER TABLE expense 
ADD CONSTRAINT fk_expense_category 
FOREIGN KEY (category_id) REFERENCES budget_category(id);

ALTER TABLE expense_allocation 
ADD CONSTRAINT fk_expense_allocation_expense 
FOREIGN KEY (expense_id) REFERENCES expense(id) ON DELETE CASCADE;

-- Notification foreign keys
ALTER TABLE notification 
ADD CONSTRAINT fk_notification_user 
FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE;

ALTER TABLE notification 
ADD CONSTRAINT fk_notification_sender 
FOREIGN KEY (sender_id) REFERENCES "user"(id);

-- Saved item foreign keys
ALTER TABLE saved_item 
ADD CONSTRAINT fk_saved_item_user 
FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE;
