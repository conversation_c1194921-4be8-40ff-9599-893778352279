-- ============================================================================
-- STEP 3: UPDATE USER-RELATED FOREIGN KEYS TO UUID
-- ============================================================================
-- This converts ONLY user-related foreign keys from BIGINT to UUID
-- - Keeps all primary keys as BIGSERIAL (auto-incrementing)
-- - Only changes columns that reference the profile table
-- - Maintains all other foreign key relationships

-- 1. UPDATE user_system_preference
-- Drop existing foreign key constraint
ALTER TABLE user_system_preference 
DROP CONSTRAINT IF EXISTS user_system_preference_user_id_fkey,
DROP CONSTRAINT IF EXISTS fk_user_system_preference_user;

-- Convert user_id column to UUID (keep id as BIGSERIAL)
ALTER TABLE user_system_preference 
ALTER COLUMN user_id TYPE UUID USING uuid_generate_v4();

-- Add new foreign key constraint to profile table
ALTER TABLE user_system_preference 
ADD CONSTRAINT fk_user_system_preference_profile 
FOREIGN KEY (user_id) REFERENCES profile(id) ON DELETE CASCADE;

-- 2. UPDATE user_interest_junction
-- Drop existing foreign key constraint
ALTER TABLE user_interest_junction 
DROP CONSTRAINT IF EXISTS user_interest_junction_user_id_fkey,
DROP CONSTRAINT IF EXISTS fk_user_interest_junction_user;

-- Convert user_id column to UUID (keep id as BIGSERIAL)
ALTER TABLE user_interest_junction 
ALTER COLUMN user_id TYPE UUID USING uuid_generate_v4();

-- Add new foreign key constraint to profile table
ALTER TABLE user_interest_junction 
ADD CONSTRAINT fk_user_interest_junction_profile 
FOREIGN KEY (user_id) REFERENCES profile(id) ON DELETE CASCADE;

-- 3. UPDATE guide_profile
-- Drop existing foreign key constraint
ALTER TABLE guide_profile 
DROP CONSTRAINT IF EXISTS guide_profile_user_id_fkey,
DROP CONSTRAINT IF EXISTS fk_guide_profile_user;

-- Convert user_id column to UUID (keep id as BIGSERIAL)
ALTER TABLE guide_profile 
ALTER COLUMN user_id TYPE UUID USING uuid_generate_v4();

-- Add new foreign key constraint to profile table
ALTER TABLE guide_profile 
ADD CONSTRAINT fk_guide_profile_profile 
FOREIGN KEY (user_id) REFERENCES profile(id) ON DELETE CASCADE;

-- 4. UPDATE trip table
-- Drop existing foreign key constraint
ALTER TABLE trip 
DROP CONSTRAINT IF EXISTS trip_user_id_fkey,
DROP CONSTRAINT IF EXISTS fk_trip_user;

-- Convert user_id column to UUID (keep id as BIGSERIAL)
ALTER TABLE trip 
ALTER COLUMN user_id TYPE UUID USING uuid_generate_v4();

-- Add new foreign key constraint to profile table
ALTER TABLE trip 
ADD CONSTRAINT fk_trip_profile 
FOREIGN KEY (user_id) REFERENCES profile(id) ON DELETE CASCADE;
