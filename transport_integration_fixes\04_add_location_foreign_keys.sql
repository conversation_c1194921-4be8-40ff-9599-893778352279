-- ============================================================================
-- PHASE 4: ADD MISSING FOREIGN KEY CONSTRAINTS FOR LOCATION-DEPENDENT TABLES
-- ============================================================================
-- Execute these to complete the location table integration

-- Add foreign key constraints for accommodation table
ALTER TABLE accommodation 
ADD CONSTRAINT fk_accommodation_location 
FOREIGN KEY (location_id) REFERENCES location(id);

-- Add foreign key constraints for activity table  
ALTER TABLE activity 
ADD CONSTRAINT fk_activity_location 
FOREIGN KEY (location_id) REFERENCES location(id);

-- Add foreign key constraints for restaurant table
ALTER TABLE restaurant 
ADD CONSTRAINT fk_restaurant_location 
FOREIGN KEY (location_id) REFERENCES location(id);

-- Ensure city foreign key constraints are properly set
ALTER TABLE guided_tour 
ADD CONSTRAINT fk_guided_tour_city 
FOREIGN KEY (city_id) REFERENCES city(id);

ALTER TABLE guide_location_junction 
ADD CONSTRAINT fk_guide_location_junction_city 
FOREIGN KEY (city_id) REFERENCES city(id);
