-- ============================================================================
-- PHASE 10: VERIFICATION QUERIES
-- ============================================================================
-- Run these queries to verify all fixes have been applied correctly

-- 1. Verify all primary keys are BIGINT
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE column_name = 'id'
    AND table_schema = 'public'
    AND data_type != 'bigint'
ORDER BY table_name;

-- 2. Verify all foreign key constraints exist
SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
ORDER BY tc.table_name, tc.constraint_name;

-- 3. Check for any remaining INTEGER foreign key columns that should be BIGINT
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns
WHERE table_schema = 'public'
    AND column_name LIKE '%_id'
    AND data_type = 'integer'
    AND table_name NOT IN ('daily_itinerary_item') -- This one might legitimately be integer
ORDER BY table_name, column_name;

-- 4. Verify transport integration - check trip_transport foreign keys
SELECT 
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS references_table
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.table_name = 'trip_transport'
    AND tc.constraint_type = 'FOREIGN KEY'
ORDER BY kcu.column_name;

-- 5. Verify image system - check entity_image table
SELECT COUNT(*) as entity_image_count FROM entity_image;
SELECT entity_type, COUNT(*) as count FROM entity_image GROUP BY entity_type;

-- 6. Check for any tables missing expected foreign keys
-- This query helps identify tables that might be missing FK constraints
SELECT 
    t.table_name,
    c.column_name
FROM information_schema.tables t
JOIN information_schema.columns c ON t.table_name = c.table_name
LEFT JOIN information_schema.key_column_usage kcu 
    ON c.table_name = kcu.table_name 
    AND c.column_name = kcu.column_name
LEFT JOIN information_schema.table_constraints tc 
    ON kcu.constraint_name = tc.constraint_name 
    AND tc.constraint_type = 'FOREIGN KEY'
WHERE t.table_schema = 'public'
    AND c.table_schema = 'public'
    AND c.column_name LIKE '%_id'
    AND c.column_name != 'id'
    AND tc.constraint_name IS NULL
    AND t.table_type = 'BASE TABLE'
ORDER BY t.table_name, c.column_name;

-- 7. Count total tables before and after
SELECT COUNT(*) as total_tables 
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE';

-- 8. Verify no old image tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_name LIKE '%_image'
    AND table_name != 'entity_image';

-- 9. Check for any constraint violations
-- This will show if there are any data integrity issues
SELECT 
    conname as constraint_name,
    conrelid::regclass as table_name
FROM pg_constraint
WHERE NOT convalidated;
