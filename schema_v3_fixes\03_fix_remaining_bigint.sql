-- ============================================================================
-- PHASE 3: FIX REMAINING TABLES BIGINT CONVERSION
-- ============================================================================
-- Convert remaining tables to BIGINT

-- Fix restaurant tables
ALTER TABLE restaurant ALTER COLUMN id TYPE BIGINT;
ALTER TABLE restaurant ALTER COLUMN type_id TYPE BIGINT;

ALTER TABLE restaurant_type ALTER COLUMN id TYPE BIGINT;
ALTER TABLE restaurant_type ALTER COLUMN icon_id TYPE BIGINT;

ALTER TABLE cuisine_type ALTER COLUMN id TYPE BIGINT;
ALTER TABLE cuisine_type ALTER COLUMN icon_id TYPE BIGINT;

ALTER TABLE restaurant_cuisine_junction ALTER COLUMN id TYPE BIGINT;
ALTER TABLE restaurant_cuisine_junction ALTER COLUMN restaurant_id TYPE BIGINT;
ALTER TABLE restaurant_cuisine_junction ALTER COLUMN cuisine_id TYPE BIGINT;

ALTER TABLE restaurant_amenity_junction ALTER COLUMN id TYPE BIGINT;
ALTER TABLE restaurant_amenity_junction ALTER COLUMN restaurant_id TYPE BIGINT;

ALTER TABLE restaurant_opening_hours ALTER COLUMN id TYPE BIGINT;
ALTER TABLE restaurant_opening_hours ALTER COLUMN restaurant_id TYPE BIGINT;

-- Fix meal type
ALTER TABLE meal_type ALTER COLUMN id TYPE BIGINT;
ALTER TABLE meal_type ALTER COLUMN icon_id TYPE BIGINT;

-- Fix tag table
ALTER TABLE tag ALTER COLUMN id TYPE BIGINT;
ALTER TABLE tag ALTER COLUMN icon_id TYPE BIGINT;

-- Fix trip tables
ALTER TABLE trip ALTER COLUMN id TYPE BIGINT;
ALTER TABLE trip ALTER COLUMN user_id TYPE BIGINT;

ALTER TABLE trip_change_log ALTER COLUMN id TYPE BIGINT;
ALTER TABLE trip_change_log ALTER COLUMN trip_id TYPE BIGINT;
ALTER TABLE trip_change_log ALTER COLUMN user_id TYPE BIGINT;
ALTER TABLE trip_change_log ALTER COLUMN entity_id TYPE BIGINT;

ALTER TABLE trip_interest_junction ALTER COLUMN id TYPE BIGINT;
ALTER TABLE trip_interest_junction ALTER COLUMN trip_id TYPE BIGINT;
ALTER TABLE trip_interest_junction ALTER COLUMN interest_id TYPE BIGINT;

ALTER TABLE trip_day ALTER COLUMN id TYPE BIGINT;
ALTER TABLE trip_day ALTER COLUMN trip_id TYPE BIGINT;

ALTER TABLE daily_itinerary ALTER COLUMN id TYPE BIGINT;
ALTER TABLE daily_itinerary ALTER COLUMN trip_day_id TYPE BIGINT;

ALTER TABLE daily_itinerary_item ALTER COLUMN id TYPE BIGINT;
ALTER TABLE daily_itinerary_item ALTER COLUMN daily_itinerary_id TYPE BIGINT;
ALTER TABLE daily_itinerary_item ALTER COLUMN item_id TYPE BIGINT;

-- Fix tour tables
ALTER TABLE tour_highlight ALTER COLUMN id TYPE BIGINT;
ALTER TABLE tour_highlight ALTER COLUMN tour_id TYPE BIGINT;

ALTER TABLE tour_itinerary_point ALTER COLUMN id TYPE BIGINT;
ALTER TABLE tour_itinerary_point ALTER COLUMN tour_id TYPE BIGINT;

-- Fix trip booking tables
ALTER TABLE trip_accommodation ALTER COLUMN id TYPE BIGINT;
ALTER TABLE trip_accommodation ALTER COLUMN trip_id TYPE BIGINT;
ALTER TABLE trip_accommodation ALTER COLUMN trip_day_id TYPE BIGINT;
ALTER TABLE trip_accommodation ALTER COLUMN accommodation_id TYPE BIGINT;
ALTER TABLE trip_accommodation ALTER COLUMN room_id TYPE BIGINT;

ALTER TABLE trip_activity ALTER COLUMN id TYPE BIGINT;
ALTER TABLE trip_activity ALTER COLUMN trip_day_id TYPE BIGINT;
ALTER TABLE trip_activity ALTER COLUMN activity_id TYPE BIGINT;

ALTER TABLE trip_transport ALTER COLUMN id TYPE BIGINT;
ALTER TABLE trip_transport ALTER COLUMN trip_day_id TYPE BIGINT;

ALTER TABLE trip_meal ALTER COLUMN id TYPE BIGINT;
ALTER TABLE trip_meal ALTER COLUMN trip_day_id TYPE BIGINT;
ALTER TABLE trip_meal ALTER COLUMN meal_type_id TYPE BIGINT;
ALTER TABLE trip_meal ALTER COLUMN restaurant_id TYPE BIGINT;

-- Fix expense tables
ALTER TABLE expense ALTER COLUMN id TYPE BIGINT;
ALTER TABLE expense ALTER COLUMN trip_id TYPE BIGINT;
ALTER TABLE expense ALTER COLUMN trip_day_id TYPE BIGINT;
ALTER TABLE expense ALTER COLUMN category_id TYPE BIGINT;

ALTER TABLE expense_allocation ALTER COLUMN expense_id TYPE BIGINT;
ALTER TABLE expense_allocation ALTER COLUMN entity_id TYPE BIGINT;

-- Fix notification and saved items
ALTER TABLE notification ALTER COLUMN id TYPE BIGINT;
ALTER TABLE notification ALTER COLUMN user_id TYPE BIGINT;
ALTER TABLE notification ALTER COLUMN sender_id TYPE BIGINT;
ALTER TABLE notification ALTER COLUMN related_entity_id TYPE BIGINT;

ALTER TABLE saved_item ALTER COLUMN id TYPE BIGINT;
ALTER TABLE saved_item ALTER COLUMN user_id TYPE BIGINT;
ALTER TABLE saved_item ALTER COLUMN entity_id TYPE BIGINT;
