-- ============================================================================
-- PHASE 5: VERIFICATION QUERIES (OPTIONAL)
-- ============================================================================
-- Run these to verify all foreign key constraints are properly established
-- These are SELECT queries for verification - not ALTER statements

-- Verify transport_mode foreign keys
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name IN ('transport_mode', 'transport_provider', 'transport_line', 'transport_fare', 'station', 'trip_transport')
ORDER BY tc.table_name, tc.constraint_name;

-- Verify location-dependent table foreign keys
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name IN ('accommodation', 'activity', 'restaurant', 'location')
ORDER BY tc.table_name, tc.constraint_name;

-- Check data types are consistent
SELECT 
    table_name,
    column_name,
    data_type,
    character_maximum_length,
    numeric_precision
FROM information_schema.columns
WHERE table_name IN ('trip', 'trip_transport', 'location', 'accommodation', 'activity', 'restaurant')
    AND column_name LIKE '%_id'
ORDER BY table_name, column_name;
