-- ============================================================================
-- STEP 8: SUPABASE DEPLOYMENT RLS POLICIES
-- ============================================================================
-- Run this file ONLY when deploying to Supabase
-- This contains the proper RLS policies with auth.uid() function
-- DO NOT run this locally - it will fail without Supabase auth schema

-- Re-enable RLS on all tables
ALTER TABLE profile ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_system_preference ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_interest_junction ENABLE ROW LEVEL SECURITY;
ALTER TABLE guide_profile ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_change_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE guide_request ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_item ENABLE ROW LEVEL SECURITY;

-- PROFILE TABLE POLICIES
CREATE POLICY "Users can view own profile" ON profile
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profile
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Anyone can view guide profiles" ON profile
    FOR SELECT USING (role IN ('guide', 'both'));

-- USER SYSTEM PREFERENCES POLICIES
CREATE POLICY "Users can manage own preferences" ON user_system_preference
    FOR ALL USING (auth.uid() = user_id);

-- USER INTERESTS POLICIES
CREATE POLICY "Users can manage own interests" ON user_interest_junction
    FOR ALL USING (auth.uid() = user_id);

-- GUIDE PROFILE POLICIES
CREATE POLICY "Guides can manage own profile" ON guide_profile
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Anyone can view guide profiles" ON guide_profile
    FOR SELECT USING (true);

-- TRIP POLICIES
CREATE POLICY "Users can manage own trips" ON trip
    FOR ALL USING (auth.uid() = user_id);

-- TRIP CHANGE LOG POLICIES
CREATE POLICY "Users can view own trip changes" ON trip_change_log
    FOR SELECT USING (auth.uid() = user_id);

-- GUIDE REQUEST POLICIES (Invitation System)
CREATE POLICY "Travelers can manage sent invitations" ON guide_request
    FOR ALL USING (auth.uid() = traveler_id);

CREATE POLICY "Guides can view received invitations" ON guide_request
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM guide_profile gp
            WHERE gp.id = guide_request.guide_id 
            AND gp.user_id = auth.uid()
        )
    );

CREATE POLICY "Guides can update invitation status" ON guide_request
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM guide_profile gp
            WHERE gp.id = guide_request.guide_id 
            AND gp.user_id = auth.uid()
        )
    );

-- NOTIFICATION POLICIES
CREATE POLICY "Users can view own notifications" ON notification
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can send notifications" ON notification
    FOR INSERT WITH CHECK (auth.uid() = sender_id);

-- SAVED ITEM POLICIES
CREATE POLICY "Users can manage own saved items" ON saved_item
    FOR ALL USING (auth.uid() = user_id);
