from create import create_connection
import psycopg2
from config import db_params

def insert_data(conn, file_path):
    cur = conn.cursor()
    try:
        with open(file_path, 'r') as file:
            cur.execute(file.read())
            conn.commit()
    except psycopg2.errors as e:
        print(f'errors inserting data: {e}')
        conn.rollback()
    finally:
        cur.close()

def main():
    conn = create_connection(db_params)
    if conn is not None:
        insert_data(conn, 'sql/transport_mode.sql')
        conn.close()
        print("Data inserted successfully!")
    else:
        print("Error inserting data.")

if __name__ == '__main__':
    main()
