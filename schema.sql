
CREATE TABLE icon (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Country
CREATE TABLE country (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL UNIQUE,
    code CHAR(2) UNIQUE,
    currency CHAR(3) NOT NULL,
    language VARCHAR(50),
    visa_requirements TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Country Emergency Contacts
CREATE TABLE country_emergency_contact (
    id SERIAL PRIMARY KEY,
    country_id INTEGER NOT NULL,
    service_type VARCHAR(50) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (country_id) REFERENCES country(id) ON DELETE CASCADE,
    UNIQUE (country_id, service_type)
);

-- Country Customs
CREATE TABLE country_custom (
    id SERIAL PRIMARY KEY,
    country_id INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    icon_id INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (country_id) REFERENCES country(id) ON DELETE CASCADE,
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- City
CREATE TABLE city (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    country_id INTEGER NOT NULL,
    timezone VARCHAR(50) NOT NULL,
    hero_image TEXT,
    short_description TEXT,
    about TEXT,
    best_time_to_visit TEXT,
    average_budget_min NUMERIC(12,2),
    average_budget_max NUMERIC(12,2),
    recommended_stay_min INTEGER,
    recommended_stay_max INTEGER,
    video_url TEXT,
    map_embed_url TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (country_id) REFERENCES country(id),
    UNIQUE (name, country_id)
);

-- Location
CREATE TABLE location (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    address TEXT,
    city_id INTEGER,
    lat DOUBLE PRECISION NOT NULL,
    lng DOUBLE PRECISION NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('hotel', 'restaurant', 'activity', 'airport', 'station', 'point_of_interest')),
    place_id VARCHAR(100) UNIQUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    UNIQUE(lat, lng),
    FOREIGN KEY (city_id) REFERENCES city(id)
);

-- Language
CREATE TABLE language (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- ============================================================================
-- USER MANAGEMENT
-- ============================================================================

-- User
CREATE TABLE "user" (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20) UNIQUE,
    avatar_url TEXT,
    role VARCHAR(20) NOT NULL DEFAULT 'traveler' CHECK (role IN ('traveler', 'guide', 'admin', 'both')),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- User System Preferences
CREATE TABLE user_system_preference (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL UNIQUE,
    default_currency CHAR(3) NOT NULL DEFAULT 'USD',
    default_language VARCHAR(50) NOT NULL DEFAULT 'English',
    timezone VARCHAR(50) NOT NULL DEFAULT 'UTC',
    date_format VARCHAR(20) NOT NULL DEFAULT 'YYYY-MM-DD',
    email_notifications BOOLEAN NOT NULL DEFAULT true,
    sms_notifications BOOLEAN NOT NULL DEFAULT false,
    push_notifications BOOLEAN NOT NULL DEFAULT true,
    trip_updates BOOLEAN NOT NULL DEFAULT true,
    marketing BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE
);

-- Interest Categories
CREATE TABLE interest_category (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    icon_id INTEGER,
    description TEXT,
    type VARCHAR(50) NOT NULL CHECK (type IN ('travel_style', 'accommodation', 'activity', 'cuisine', 'general')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- User Interest Junction
CREATE TABLE user_interest_junction (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    interest_id INTEGER NOT NULL,
    weight INTEGER NOT NULL DEFAULT 5 CHECK (weight >= 1 AND weight <= 10),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE,
    FOREIGN KEY (interest_id) REFERENCES interest_category(id) ON DELETE CASCADE,
    UNIQUE (user_id, interest_id)
);

-- ============================================================================
-- GUIDE PROFILES
-- ============================================================================

-- Guide Profile
CREATE TABLE guide_profile (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL UNIQUE,
    bio TEXT NOT NULL,
    specialization VARCHAR(200) NOT NULL,
    experience_years INTEGER NOT NULL,
    hourly_rate NUMERIC(10,2) NOT NULL,
    full_day_rate NUMERIC(10,2) NOT NULL,
    category VARCHAR(100),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE
);

-- Guide Language Junction
CREATE TABLE guide_language_junction (
    id SERIAL PRIMARY KEY,
    guide_id INTEGER NOT NULL,
    language_id INTEGER NOT NULL,
    proficiency VARCHAR(20) NOT NULL CHECK (proficiency IN ('basic', 'conversational', 'fluent', 'native')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE,
    FOREIGN KEY (language_id) REFERENCES language(id),
    UNIQUE (guide_id, language_id)
);

-- Guide Availability
CREATE TABLE guide_availability (
    id SERIAL PRIMARY KEY,
    guide_id INTEGER NOT NULL,
    date DATE NOT NULL,
    available_from TIME,
    available_to TIME,
    is_full_day BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE,
    UNIQUE (guide_id, date)
);

-- Guide Specialty
CREATE TABLE guide_specialty (
    id SERIAL PRIMARY KEY,
    guide_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    icon_id INTEGER,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE,
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Guide Location Junction
CREATE TABLE guide_location_junction (
    id SERIAL PRIMARY KEY,
    guide_id INTEGER NOT NULL,
    city_id INTEGER NOT NULL,
    is_primary BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE,
    FOREIGN KEY (city_id) REFERENCES city(id),
    UNIQUE (guide_id, city_id)
);

-- ============================================================================
-- TRANSPORTATION ENTITIES
-- ============================================================================

-- Transport Mode
CREATE TABLE transport_mode (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    icon_id BIGINT,
    description TEXT,
    is_public BOOLEAN NOT NULL DEFAULT TRUE,
    is_local BOOLEAN NOT NULL DEFAULT TRUE,
    allows_free_transfers BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Transport Provider
CREATE TABLE transport_provider (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    website TEXT,
    contact_phone TEXT,
    logo_url TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Station
CREATE TABLE station (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    location_id BIGINT NOT NULL,
    station_type TEXT NOT NULL,
    is_transfer_point BOOLEAN NOT NULL DEFAULT FALSE,
    station_code CHAR(3) UNIQUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (location_id) REFERENCES location(id)
);

-- Transport Line
CREATE TABLE transport_line (
    id SERIAL PRIMARY KEY,
    provider_id BIGINT NOT NULL,
    mode_id BIGINT NOT NULL,
    line_number TEXT NOT NULL,
    name TEXT,
    city_id BIGINT,
    is_intercity BOOLEAN NOT NULL DEFAULT FALSE,
    train_type TEXT,
    color TEXT,
    fare_type TEXT NOT NULL DEFAULT 'fixed',
    fare_amount NUMERIC(12,2),
    currency CHAR(3),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (provider_id) REFERENCES transport_provider(id),
    FOREIGN KEY (mode_id) REFERENCES transport_mode(id),
    FOREIGN KEY (city_id) REFERENCES city(id),
    UNIQUE (provider_id, line_number, city_id)
);

-- Line Station Junction
CREATE TABLE line_station_junction (
    id SERIAL PRIMARY KEY,
    line_id BIGINT NOT NULL,
    station_id BIGINT NOT NULL,
    sequence INT NOT NULL,
    is_terminus BOOLEAN NOT NULL DEFAULT FALSE,
    platform TEXT,
    travel_time_to_next INT,
    distance_to_next NUMERIC(8,2),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (line_id) REFERENCES transport_line(id),
    FOREIGN KEY (station_id) REFERENCES station(id),
    UNIQUE (line_id, station_id, sequence)
);

-- Station Transfer
CREATE TABLE station_transfer (
    id SERIAL PRIMARY KEY,
    from_station_id BIGINT NOT NULL,
    to_station_id BIGINT NOT NULL,
    transfer_time_minutes INT NOT NULL,
    transfer_type TEXT NOT NULL DEFAULT 'walking',
    is_free BOOLEAN NOT NULL DEFAULT TRUE,
    transfer_instructions TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (from_station_id) REFERENCES station(id),
    FOREIGN KEY (to_station_id) REFERENCES station(id),
    UNIQUE (from_station_id, to_station_id)
);

-- Transport Fare
CREATE TABLE transport_fare (
    id SERIAL PRIMARY KEY,
    mode_id BIGINT NOT NULL,
    provider_id BIGINT,
    from_station_id BIGINT,
    to_station_id BIGINT,
    city_id BIGINT,
    line_id BIGINT,
    service_class TEXT,
    price NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL,
    valid_minutes INT,
    duration_minutes INT,
    distance_km NUMERIC(8,2),
    departure_time TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (mode_id) REFERENCES transport_mode(id),
    FOREIGN KEY (provider_id) REFERENCES transport_provider(id),
    FOREIGN KEY (from_station_id) REFERENCES station(id),
    FOREIGN KEY (to_station_id) REFERENCES station(id),
    FOREIGN KEY (city_id) REFERENCES city(id),
    FOREIGN KEY (line_id) REFERENCES transport_line(id)
);

-- Car Rental
CREATE TABLE car_rental (
    id SERIAL PRIMARY KEY,
    provider_id BIGINT NOT NULL,
    city_id BIGINT NOT NULL,
    location_id BIGINT NOT NULL,
    car_type TEXT NOT NULL,
    daily_rate NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL,
    insurance_cost NUMERIC(12,2),
    is_airport_pickup BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (provider_id) REFERENCES transport_provider(id),
    FOREIGN KEY (city_id) REFERENCES city(id),
    FOREIGN KEY (location_id) REFERENCES location(id)
);

-- Taxi Fare
CREATE TABLE taxi_fare (
    id SERIAL PRIMARY KEY,
    city_id BIGINT NOT NULL,
    base_fare NUMERIC(10,2) NOT NULL,
    per_km_rate NUMERIC(10,2) NOT NULL,
    per_minute_rate NUMERIC(10,2),
    currency CHAR(3) NOT NULL,
    provider_id BIGINT,
    taxi_type TEXT DEFAULT 'petit_taxi', -- Added for Morocco-specific context
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (city_id) REFERENCES city(id),
    FOREIGN KEY (provider_id) REFERENCES transport_provider(id)
);

-- ============================================================================
-- ACCOMMODATION ENTITIES
-- ============================================================================

-- Accommodation Type
CREATE TABLE accommodation_type (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    icon_id INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Accommodation
CREATE TABLE accommodation (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    location_id INTEGER NOT NULL,
    type_id INTEGER NOT NULL,
    stars INTEGER CHECK (stars >= 1 AND stars <= 5),
    average_score NUMERIC(3,1) CHECK (average_score >= 0 AND average_score <= 10),
    description TEXT,
    check_in_time TIME,
    check_out_time TIME,
    website TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (location_id) REFERENCES location(id),
    FOREIGN KEY (type_id) REFERENCES accommodation_type(id)
);

-- Accommodation Images
CREATE TABLE accommodation_image (
    id SERIAL PRIMARY KEY,
    accommodation_id INTEGER NOT NULL,
    image_url TEXT NOT NULL,
    caption TEXT,
    is_featured BOOLEAN DEFAULT false,
    sequence INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (accommodation_id) REFERENCES accommodation(id) ON DELETE CASCADE
);

-- Amenity
CREATE TABLE amenity (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    category VARCHAR(50) NOT NULL,
    icon_id INTEGER,
    description TEXT,
    amenity_type VARCHAR(20) NOT NULL DEFAULT 'both' CHECK (amenity_type IN ('accommodation', 'restaurant', 'both')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Accommodation Amenity Junction
CREATE TABLE accommodation_amenity_junction (
    id SERIAL PRIMARY KEY,
    accommodation_id INTEGER NOT NULL,
    amenity_id INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (accommodation_id) REFERENCES accommodation(id) ON DELETE CASCADE,
    FOREIGN KEY (amenity_id) REFERENCES amenity(id),
    UNIQUE (accommodation_id, amenity_id)
);

-- Accommodation Room
CREATE TABLE accommodation_room (
    id SERIAL PRIMARY KEY,
    accommodation_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    max_guests INTEGER NOT NULL,
    beds_description TEXT,
    size_sqm INTEGER,
    image TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (accommodation_id) REFERENCES accommodation(id) ON DELETE CASCADE
);

-- Accommodation Rate
CREATE TABLE accommodation_rate (
    id SERIAL PRIMARY KEY,
    accommodation_id INTEGER NOT NULL,
    room_id INTEGER NOT NULL,
    date_from DATE NOT NULL,
    date_to DATE NOT NULL,
    base_price NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL,
    breakfast_included BOOLEAN NOT NULL DEFAULT false,
    refundable BOOLEAN NOT NULL DEFAULT true,
    cancellation_policy TEXT,
    availability INTEGER, -- Number of rooms available
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (accommodation_id) REFERENCES accommodation(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES accommodation_room(id) ON DELETE CASCADE
);

-- ============================================================================
-- ACTIVITY ENTITIES
-- ============================================================================

-- Activity Category
CREATE TABLE activity_category (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon_id INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Activity
CREATE TABLE activity (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    location_id INTEGER NOT NULL,
    category_id INTEGER NOT NULL,
    description TEXT NOT NULL,
    duration_minutes INTEGER NOT NULL,
    price_adult NUMERIC(10,2),
    price_child NUMERIC(10,2),
    currency CHAR(3),
    website TEXT,
    phone VARCHAR(20),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (location_id) REFERENCES location(id),
    FOREIGN KEY (category_id) REFERENCES activity_category(id)
);

-- Activity Images
CREATE TABLE activity_image (
    id SERIAL PRIMARY KEY,
    activity_id INTEGER NOT NULL,
    image_url TEXT NOT NULL,
    caption TEXT,
    is_featured BOOLEAN DEFAULT false,
    sequence INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (activity_id) REFERENCES activity(id) ON DELETE CASCADE
);

-- Activity Opening Hours
CREATE TABLE activity_opening_hours (
    id SERIAL PRIMARY KEY,
    activity_id INTEGER NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=Sunday
    opening_time TIME,
    closing_time TIME,
    is_closed BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (activity_id) REFERENCES activity(id) ON DELETE CASCADE,
    UNIQUE (activity_id, day_of_week)
);

-- Tag
CREATE TABLE tag (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    category VARCHAR(50),
    icon_id INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Activity Tag Junction
CREATE TABLE activity_tag_junction (
    id SERIAL PRIMARY KEY,
    activity_id INTEGER NOT NULL,
    tag_id INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (activity_id) REFERENCES activity(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tag(id),
    UNIQUE (activity_id, tag_id)
);

-- ============================================================================
-- FOOD & DINING ENTITIES
-- ============================================================================

-- Cuisine Type
CREATE TABLE cuisine_type (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    icon_id INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Restaurant Type
CREATE TABLE restaurant_type (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    icon_id INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Restaurant
CREATE TABLE restaurant (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    location_id INTEGER NOT NULL,
    type_id INTEGER NOT NULL,
    price_range INTEGER NOT NULL CHECK (price_range >= 1 AND price_range <= 4),
    rating NUMERIC(2,1) CHECK (rating >= 0 AND rating <= 5),
    description TEXT,
    website TEXT,
    phone VARCHAR(20),
    menu_url TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (location_id) REFERENCES location(id),
    FOREIGN KEY (type_id) REFERENCES restaurant_type(id)
);

-- Restaurant Cuisine Junction
CREATE TABLE restaurant_cuisine_junction (
    id SERIAL PRIMARY KEY,
    restaurant_id INTEGER NOT NULL,
    cuisine_id INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE,
    FOREIGN KEY (cuisine_id) REFERENCES cuisine_type(id),
    UNIQUE (restaurant_id, cuisine_id)
);

-- Restaurant Amenity Junction
CREATE TABLE restaurant_amenity_junction (
    id SERIAL PRIMARY KEY,
    restaurant_id INTEGER NOT NULL,
    amenity_id INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE,
    FOREIGN KEY (amenity_id) REFERENCES amenity(id),
    UNIQUE (restaurant_id, amenity_id)
);

-- Restaurant Images
CREATE TABLE restaurant_image (
    id SERIAL PRIMARY KEY,
    restaurant_id INTEGER NOT NULL,
    image_url TEXT NOT NULL,
    caption TEXT,
    is_featured BOOLEAN DEFAULT false,
    sequence INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE
);

-- Restaurant Opening Hours
CREATE TABLE restaurant_opening_hours (
    id SERIAL PRIMARY KEY,
    restaurant_id INTEGER NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=Sunday
    opening_time TIME,
    closing_time TIME,
    is_closed BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE,
    UNIQUE (restaurant_id, day_of_week)
);

-- Meal Type
CREATE TABLE meal_type (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    icon_id INTEGER,
    typical_time_start TIME, -- e.g., '07:00'
    typical_time_end TIME,   -- e.g., '10:00'
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- ============================================================================
-- TRIP MANAGEMENT
-- ============================================================================

-- Trip
CREATE TABLE trip (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    image_url TEXT,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    budget_total NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL,
    origin_airport_id INTEGER, -- Reference to Station with type 'airport'
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'confirmed', 'in_progress', 'completed', 'cancelled')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (user_id) REFERENCES "user"(id),
    FOREIGN KEY (origin_airport_id) REFERENCES station(id),
    CHECK (end_date >= start_date)
);

-- Trip Change Log
CREATE TABLE trip_change_log (
    id SERIAL PRIMARY KEY,
    trip_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    change_type VARCHAR(30) NOT NULL CHECK (change_type IN ('created', 'updated', 'cancelled', 'rescheduled', 'budget_changed', 'item_added', 'item_removed', 'item_modified')),
    entity_type VARCHAR(20) NOT NULL CHECK (entity_type IN ('trip', 'accommodation', 'activity', 'meal', 'transport')),
    entity_id INTEGER NOT NULL,
    field_name VARCHAR(50) NOT NULL,
    old_value TEXT,
    new_value TEXT,
    change_reason TEXT,
    system_note TEXT,
    user_note TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES "user"(id)
);

-- Trip Interest Junction
CREATE TABLE trip_interest_junction (
    id SERIAL PRIMARY KEY,
    trip_id INTEGER NOT NULL,
    interest_id INTEGER NOT NULL,
    weight INTEGER NOT NULL DEFAULT 5 CHECK (weight >= 1 AND weight <= 10),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE,
    FOREIGN KEY (interest_id) REFERENCES interest_category(id),
    UNIQUE (trip_id, interest_id)
);

-- Trip Day
CREATE TABLE trip_day (
    id SERIAL PRIMARY KEY,
    trip_id INTEGER NOT NULL,
    day_number INTEGER NOT NULL,
    date DATE NOT NULL,
    city_id INTEGER NOT NULL,
    daily_budget NUMERIC(12,2),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE,
    FOREIGN KEY (city_id) REFERENCES city(id),
    UNIQUE (trip_id, day_number),
    UNIQUE (trip_id, date)
);

-- Daily Itinerary
CREATE TABLE daily_itinerary (
    id SERIAL PRIMARY KEY,
    trip_day_id INTEGER NOT NULL UNIQUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (trip_day_id) REFERENCES trip_day(id) ON DELETE CASCADE
);

-- Daily Itinerary Item
CREATE TABLE daily_itinerary_item
(
    id                 SERIAL PRIMARY KEY,
    daily_itinerary_id INTEGER     NOT NULL,
    sequence           INTEGER     NOT NULL,
    item_type          VARCHAR(20) NOT NULL CHECK (item_type IN
                                                   ('activity', 'meal', 'transport', 'free_time', 'guided_tour')),
    item_id            INTEGER, -- ID of referenced item
    start_time         TIME,
    end_time           TIME,
    notes              TEXT,
    created_at         TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at         TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (daily_itinerary_id) REFERENCES daily_itinerary(id) ON DELETE CASCADE
);

-- Guided Tour
CREATE TABLE guided_tour (
    id SERIAL PRIMARY KEY,
    guide_id INTEGER NOT NULL,
    city_id INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    duration_hours NUMERIC(4,2) NOT NULL,
    max_travelers INTEGER NOT NULL DEFAULT 10,
    base_price NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL DEFAULT 'USD',
    meeting_point TEXT,
    includes TEXT,
    excludes TEXT,
    is_private BOOLEAN NOT NULL DEFAULT false,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (guide_id) REFERENCES guide_profile(id) ON DELETE CASCADE,
    FOREIGN KEY (city_id) REFERENCES city(id)
);

-- Tour Highlight
CREATE TABLE tour_highlight (
    id SERIAL PRIMARY KEY,
    tour_id INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    sequence INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (tour_id) REFERENCES guided_tour(id) ON DELETE CASCADE
);

-- Tour Image
CREATE TABLE tour_image (
    id SERIAL PRIMARY KEY,
    tour_id INTEGER NOT NULL,
    image_url TEXT NOT NULL,
    caption TEXT,
    is_featured BOOLEAN DEFAULT false,
    sequence INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (tour_id) REFERENCES guided_tour(id) ON DELETE CASCADE
);

-- Tour Itinerary Point
CREATE TABLE tour_itinerary_point (
    id SERIAL PRIMARY KEY,
    tour_id INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    duration_minutes INTEGER NOT NULL,
    sequence INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (tour_id) REFERENCES guided_tour(id) ON DELETE CASCADE
);

-- Guide Request
CREATE TABLE guide_request (
    id SERIAL PRIMARY KEY,
    traveler_id INTEGER NOT NULL,
    guide_id INTEGER NOT NULL,
    trip_id INTEGER,
    tour_id INTEGER,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    travelers_count INTEGER NOT NULL DEFAULT 1,
    amount NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'completed', 'cancelled')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (traveler_id) REFERENCES "user"(id),
    FOREIGN KEY (guide_id) REFERENCES guide_profile(id),
    FOREIGN KEY (trip_id) REFERENCES trip(id),
    FOREIGN KEY (tour_id) REFERENCES guided_tour(id)
);

-- ============================================================================
-- MISSING TRIP BOOKING ENTITIES
-- ============================================================================

-- Trip Accommodation
CREATE TABLE trip_accommodation (
    id SERIAL PRIMARY KEY,
    trip_id INTEGER NOT NULL,
    trip_day_id INTEGER NOT NULL,
    accommodation_id INTEGER NOT NULL,
    room_id INTEGER NOT NULL,
    check_in_date DATE NOT NULL,
    check_out_date DATE NOT NULL,
    nights INTEGER NOT NULL,
    guests INTEGER NOT NULL DEFAULT 1,
    price_per_night NUMERIC(12,2) NOT NULL,
    total_price NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL,
    booking_reference VARCHAR(100),
    booking_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (booking_status IN ('confirmed', 'pending', 'cancelled')),
    special_requests TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE,
    FOREIGN KEY (trip_day_id) REFERENCES trip_day(id) ON DELETE CASCADE,
    FOREIGN KEY (accommodation_id) REFERENCES accommodation(id),
    FOREIGN KEY (room_id) REFERENCES accommodation_room(id),
    UNIQUE (trip_id, trip_day_id, accommodation_id, room_id)
);

-- Trip Activity
CREATE TABLE trip_activity (
    id SERIAL PRIMARY KEY,
    trip_day_id INTEGER NOT NULL,
    activity_id INTEGER NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    description TEXT,
    total_price NUMERIC(12,2),
    currency CHAR(3),
    booking_status VARCHAR(20) NOT NULL DEFAULT 'recommended' CHECK (booking_status IN ('confirmed', 'pending', 'recommended')),
    booking_reference VARCHAR(100),
    meeting_point TEXT,
    notes TEXT,
    sequence INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (trip_day_id) REFERENCES trip_day(id) ON DELETE CASCADE,
    FOREIGN KEY (activity_id) REFERENCES activity(id)
);

-- Trip Transport
CREATE TABLE trip_transport (
    id SERIAL PRIMARY KEY,
    trip_day_id INTEGER NOT NULL,
    mode_id INTEGER NOT NULL,
    from_location_id INTEGER NOT NULL,
    to_location_id INTEGER NOT NULL,
    departure_time TIMESTAMPTZ NOT NULL,
    arrival_time TIMESTAMPTZ NOT NULL,
    provider_id INTEGER,
    line_id INTEGER,
    fare_id INTEGER,
    price NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL,
    booking_reference VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'recommended' CHECK (status IN ('confirmed', 'pending', 'recommended')),
    sequence INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (trip_day_id) REFERENCES trip_day(id) ON DELETE CASCADE,
    FOREIGN KEY (mode_id) REFERENCES transport_mode(id),
    FOREIGN KEY (from_location_id) REFERENCES location(id),
    FOREIGN KEY (to_location_id) REFERENCES location(id),
    FOREIGN KEY (provider_id) REFERENCES transport_provider(id),
    FOREIGN KEY (line_id) REFERENCES transport_line(id),
    FOREIGN KEY (fare_id) REFERENCES transport_fare(id)
);

-- Trip Meal
CREATE TABLE trip_meal (
    id SERIAL PRIMARY KEY,
    trip_day_id INTEGER NOT NULL,
    meal_type_id INTEGER NOT NULL,
    restaurant_id INTEGER NOT NULL,
    time TIME NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    description TEXT,
    price_per_person NUMERIC(10,2),
    total_price NUMERIC(12,2),
    currency CHAR(3),
    reservation_status VARCHAR(20) NOT NULL DEFAULT 'none' CHECK (reservation_status IN ('confirmed', 'recommended', 'none')),
    reservation_time TIME,
    reservation_reference VARCHAR(100),
    notes TEXT,
    sequence INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (trip_day_id) REFERENCES trip_day(id) ON DELETE CASCADE,
    FOREIGN KEY (meal_type_id) REFERENCES meal_type(id),
    FOREIGN KEY (restaurant_id) REFERENCES restaurant(id)
);

-- ============================================================================
-- MISSING BUDGET & EXPENSE ENTITIES
-- ============================================================================

-- Budget Category
CREATE TABLE budget_category (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    icon_id INTEGER,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (icon_id) REFERENCES icon(id)
);

-- Budget Allocation
CREATE TABLE budget_allocation (
    id SERIAL PRIMARY KEY,
    trip_id INTEGER NOT NULL,
    category_id INTEGER NOT NULL,
    allocated_amount NUMERIC(12,2) NOT NULL,
    spent_amount NUMERIC(12,2) NOT NULL DEFAULT 0,
    currency CHAR(3) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES budget_category(id),
    UNIQUE (trip_id, category_id)
);

-- Expense
CREATE TABLE expense (
    id SERIAL PRIMARY KEY,
    trip_id INTEGER NOT NULL,
    trip_day_id INTEGER,
    category_id INTEGER NOT NULL,
    amount NUMERIC(12,2) NOT NULL,
    currency CHAR(3) NOT NULL,
    description TEXT,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    payment_method VARCHAR(50),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (trip_id) REFERENCES trip(id) ON DELETE CASCADE,
    FOREIGN KEY (trip_day_id) REFERENCES trip_day(id),
    FOREIGN KEY (category_id) REFERENCES budget_category(id)
);

-- Expense Allocation
CREATE TABLE expense_allocation (
    expense_id INTEGER NOT NULL,
    entity_type VARCHAR(20) NOT NULL CHECK (entity_type IN ('accommodation', 'activity', 'meal', 'transport', 'other')),
    entity_id INTEGER NOT NULL,
    amount NUMERIC(12,2) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (expense_id) REFERENCES expense(id) ON DELETE CASCADE,
    PRIMARY KEY (expense_id, entity_type, entity_id)
);

-- ============================================================================
-- MISSING NOTIFICATION & SAVED ITEMS ENTITIES
-- ============================================================================

-- Notification
CREATE TABLE notification (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('message', 'trip_update', 'system', 'promotion')),
    sender_id INTEGER,
    related_entity_type VARCHAR(50),
    related_entity_id INTEGER,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_read BOOLEAN NOT NULL DEFAULT false,
    action_url TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES "user"(id)
);

-- Saved Item
CREATE TABLE saved_item (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    entity_type VARCHAR(20) NOT NULL CHECK (entity_type IN ('city', 'hotel', 'restaurant', 'activity', 'attraction', 'guided_tour')),
    entity_id INTEGER NOT NULL,
    notes TEXT,
    saved_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE,
    UNIQUE (user_id, entity_type, entity_id)
);

